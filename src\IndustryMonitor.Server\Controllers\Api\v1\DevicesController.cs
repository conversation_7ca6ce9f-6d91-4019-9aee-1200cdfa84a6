using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using IndustryMonitor.Server.Services.Device;
using IndustryMonitor.Shared.Models.DTOs;
using FluentValidation;
using System.ComponentModel.DataAnnotations;

namespace IndustryMonitor.Server.Controllers.Api.v1;

/// <summary>
/// 设备管理 API 控制器
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class DevicesController : ControllerBase
{
    private readonly IDeviceService _deviceService;
    private readonly ILogger<DevicesController> _logger;

    public DevicesController(IDeviceService deviceService, ILogger<DevicesController> logger)
    {
        _deviceService = deviceService;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有设备
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<IEnumerable<DeviceDto>>>> GetAllDevices()
    {
        try
        {
            var devices = await _deviceService.GetAllDevicesAsync();
            return Ok(ApiResponse<IEnumerable<DeviceDto>>.Success(devices, "获取设备列表成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有设备失败");
            return StatusCode(500, ApiResponse<object>.Error("获取设备列表失败"));
        }
    }

    /// <summary>
    /// 获取活跃设备
    /// </summary>
    [HttpGet("active")]
    public async Task<ActionResult<ApiResponse<IEnumerable<DeviceDto>>>> GetActiveDevices()
    {
        try
        {
            var devices = await _deviceService.GetActiveDevicesAsync();
            return Ok(ApiResponse<IEnumerable<DeviceDto>>.Success(devices, "获取活跃设备列表成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取活跃设备失败");
            return StatusCode(500, ApiResponse<object>.Error("获取活跃设备列表失败"));
        }
    }

    /// <summary>
    /// 根据ID获取设备
    /// </summary>
    [HttpGet("{id:int}")]
    public async Task<ActionResult<ApiResponse<DeviceDto>>> GetDevice(int id)
    {
        try
        {
            var device = await _deviceService.GetDeviceAsync(id);
            if (device == null)
            {
                return NotFound(ApiResponse<object>.Error("设备不存在"));
            }

            return Ok(ApiResponse<DeviceDto>.Success(device, "获取设备信息成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备失败: DeviceId = {DeviceId}", id);
            return StatusCode(500, ApiResponse<object>.Error("获取设备信息失败"));
        }
    }

    /// <summary>
    /// 创建设备
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,Operator")]
    public async Task<ActionResult<ApiResponse<DeviceDto>>> CreateDevice([FromBody] CreateDeviceRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<object>.Error("输入数据无效", GetModelStateErrors()));
            }

            var device = await _deviceService.CreateDeviceAsync(request);
            return CreatedAtAction(nameof(GetDevice), new { id = device.Id }, 
                ApiResponse<DeviceDto>.Success(device, "创建设备成功"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "创建设备业务逻辑错误");
            return BadRequest(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建设备失败");
            return StatusCode(500, ApiResponse<object>.Error("创建设备失败"));
        }
    }

    /// <summary>
    /// 更新设备
    /// </summary>
    [HttpPut("{id:int}")]
    [Authorize(Roles = "Admin,Operator")]
    public async Task<ActionResult<ApiResponse<DeviceDto>>> UpdateDevice(int id, [FromBody] UpdateDeviceRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<object>.Error("输入数据无效", GetModelStateErrors()));
            }

            var device = await _deviceService.UpdateDeviceAsync(id, request);
            return Ok(ApiResponse<DeviceDto>.Success(device, "更新设备成功"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "更新设备业务逻辑错误: DeviceId = {DeviceId}", id);
            return BadRequest(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新设备失败: DeviceId = {DeviceId}", id);
            return StatusCode(500, ApiResponse<object>.Error("更新设备失败"));
        }
    }

    /// <summary>
    /// 删除设备
    /// </summary>
    [HttpDelete("{id:int}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<object>>> DeleteDevice(int id)
    {
        try
        {
            await _deviceService.DeleteDeviceAsync(id);
            return Ok(ApiResponse<object>.Success(null, "删除设备成功"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "删除设备业务逻辑错误: DeviceId = {DeviceId}", id);
            return BadRequest(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除设备失败: DeviceId = {DeviceId}", id);
            return StatusCode(500, ApiResponse<object>.Error("删除设备失败"));
        }
    }

    /// <summary>
    /// 获取设备状态
    /// </summary>
    [HttpGet("{id:int}/status")]
    public async Task<ActionResult<ApiResponse<DeviceStatusDto>>> GetDeviceStatus(int id)
    {
        try
        {
            var status = await _deviceService.GetDeviceStatusAsync(id);
            return Ok(ApiResponse<DeviceStatusDto>.Success(status, "获取设备状态成功"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "获取设备状态业务逻辑错误: DeviceId = {DeviceId}", id);
            return NotFound(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备状态失败: DeviceId = {DeviceId}", id);
            return StatusCode(500, ApiResponse<object>.Error("获取设备状态失败"));
        }
    }

    /// <summary>
    /// 获取设备历史数据
    /// </summary>
    [HttpGet("{id:int}/history")]
    public async Task<ActionResult<ApiResponse<IEnumerable<DeviceHistoryDto>>>> GetDeviceHistory(
        int id,
        [FromQuery] DateTime startTime,
        [FromQuery] DateTime endTime)
    {
        try
        {
            if (endTime <= startTime)
            {
                return BadRequest(ApiResponse<object>.Error("结束时间必须大于开始时间"));
            }

            if ((endTime - startTime).TotalDays > 30)
            {
                return BadRequest(ApiResponse<object>.Error("查询时间范围不能超过30天"));
            }

            var history = await _deviceService.GetDeviceHistoryAsync(id, startTime, endTime);
            return Ok(ApiResponse<IEnumerable<DeviceHistoryDto>>.Success(history, "获取设备历史数据成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备历史数据失败: DeviceId = {DeviceId}", id);
            return StatusCode(500, ApiResponse<object>.Error("获取设备历史数据失败"));
        }
    }

    /// <summary>
    /// 读取设备寄存器数据
    /// </summary>
    [HttpPost("{id:int}/read")]
    [Authorize(Roles = "Admin,Operator")]
    public async Task<ActionResult<ApiResponse<ushort[]>>> ReadDeviceRegisters(int id)
    {
        try
        {
            var data = await _deviceService.ReadDeviceRegistersAsync(id);
            return Ok(ApiResponse<ushort[]>.Success(data, "读取设备寄存器成功"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "读取设备寄存器业务逻辑错误: DeviceId = {DeviceId}", id);
            return BadRequest(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "读取设备寄存器失败: DeviceId = {DeviceId}", id);
            return StatusCode(500, ApiResponse<object>.Error("读取设备寄存器失败"));
        }
    }

    /// <summary>
    /// 写入设备寄存器数据
    /// </summary>
    [HttpPost("{id:int}/write")]
    [Authorize(Roles = "Admin,Operator")]
    public async Task<ActionResult<ApiResponse<object>>> WriteDeviceRegisters(
        int id, 
        [FromBody] Dictionary<int, ushort> registerValues)
    {
        try
        {
            if (registerValues == null || !registerValues.Any())
            {
                return BadRequest(ApiResponse<object>.Error("寄存器数据不能为空"));
            }

            if (registerValues.Count > 100)
            {
                return BadRequest(ApiResponse<object>.Error("单次写入寄存器数量不能超过100个"));
            }

            await _deviceService.WriteDeviceRegistersAsync(id, registerValues);
            return Ok(ApiResponse<object>.Success(null, "写入设备寄存器成功"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "写入设备寄存器业务逻辑错误: DeviceId = {DeviceId}", id);
            return BadRequest(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "写入设备寄存器失败: DeviceId = {DeviceId}", id);
            return StatusCode(500, ApiResponse<object>.Error("写入设备寄存器失败"));
        }
    }

    /// <summary>
    /// 测试设备连接
    /// </summary>
    [HttpPost("test-connection")]
    [Authorize(Roles = "Admin,Operator")]
    public async Task<ActionResult<ApiResponse<bool>>> TestConnection([FromBody] TestConnectionRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<object>.Error("输入数据无效", GetModelStateErrors()));
            }

            var result = await _deviceService.TestDeviceConnectionAsync(request.IpAddress, request.Port);
            var message = result ? "设备连接正常" : "设备连接失败";
            
            return Ok(ApiResponse<bool>.Success(result, message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "测试设备连接失败: {IpAddress}:{Port}", request.IpAddress, request.Port);
            return StatusCode(500, ApiResponse<object>.Error("测试设备连接失败"));
        }
    }

    private Dictionary<string, string[]> GetModelStateErrors()
    {
        return ModelState
            .Where(x => x.Value?.Errors?.Count > 0)
            .ToDictionary(
                kvp => kvp.Key,
                kvp => kvp.Value?.Errors?.Select(e => e.ErrorMessage).ToArray() ?? Array.Empty<string>()
            );
    }
}

/// <summary>
/// 测试连接请求
/// </summary>
public class TestConnectionRequest
{
    [Required(ErrorMessage = "IP地址不能为空")]
    [RegularExpression(@"^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$", ErrorMessage = "IP地址格式不正确")]
    public string IpAddress { get; set; } = "";

    [Range(1, 65535, ErrorMessage = "端口号必须在1-65535之间")]
    public int Port { get; set; } = 502;
}