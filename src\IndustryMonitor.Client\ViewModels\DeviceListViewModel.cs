using IndustryMonitor.Client.Models;
using Prism.Mvvm;
using System.Collections.ObjectModel;

namespace IndustryMonitor.Client.ViewModels
{
    public class DeviceListViewModel : BindableBase
    {
        private ObservableCollection<DeviceModel> _devices = new();
        public ObservableCollection<DeviceModel> Devices
        {
            get => _devices;
            set => SetProperty(ref _devices, value);
        }

        public DeviceListViewModel()
        {
            // For demonstration, we'll add some sample data
            // In a real application, this data would come from an API service
            Devices.Add(new DeviceModel
            {
                Id = 1,
                Name = "Device 1",
                IpAddress = "*************",
                Port = 502,
                Status = "Online",
                LastUpdated = DateTime.Now.AddSeconds(-10)
            });

            Devices.Add(new DeviceModel
            {
                Id = 2,
                Name = "Device 2",
                IpAddress = "*************",
                Port = 502,
                Status = "Offline",
                LastUpdated = DateTime.Now.AddMinutes(-5)
            });
        }
    }
}