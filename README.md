# 工业设备监控与配方下发系统

[![.NET Version](https://img.shields.io/badge/.NET-8.0-blue.svg)](https://dotnet.microsoft.com/download/dotnet/8.0)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](/)

一套专业的工业设备监控与配方管理系统，支持200台PLC设备的实时监控和配方批量下发。

## 🚀 项目特性

### 核心功能
- **实时监控**: 500ms周期稳定采集200台设备数据
- **配方管理**: 完整的配方生命周期管理，支持版本控制和锁定编辑
- **批量下发**: 智能的配方批量下发，支持2000+寄存器写入
- **实时推送**: 基于SignalR的实时数据推送和任务进度更新
- **故障隔离**: 单设备故障不影响其他设备正常运行

### 技术亮点
- **高并发**: 80个并发Modbus连接，支持大规模设备接入
- **多级缓存**: 内存+Redis+SQLite三级缓存架构
- **连接池**: 智能连接池管理，自动清理空闲连接
- **桌面应用**: WPF客户端，Material Design风格界面
- **易部署**: 服务端集中部署，客户端零配置安装

## 📋 系统要求

### 服务端
- Windows Server 2016+ 或 Windows 10/11
- .NET 8.0 Runtime
- Redis 6.0+
- 内存: 4GB+ 推荐
- 网络: 千兆以太网

### 客户端
- Windows 10/11
- .NET 8.0 Desktop Runtime
- 内存: 2GB+
- 屏幕分辨率: 1920×1080+

## 🛠️ 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/your-org/IndustryMonitor.git
cd IndustryMonitor
```

### 2. 服务端部署

#### 安装依赖
```powershell
# 安装 .NET 8 Runtime
# 下载: https://dotnet.microsoft.com/download/dotnet/8.0

# 安装 Redis (可选，也可使用嵌入式版本)
choco install redis-64
```

#### 配置服务端
```bash
cd IndustryMonitor.Server

# 编辑配置文件
notepad appsettings.json
```

配置示例：
```json
{
  "ConnectionStrings": {
    "SQLite": "Data Source=industry.db",
    "Redis": "localhost:6379"
  },
  "ModbusSettings": {
    "MaxReadConnections": 80,
    "MaxWriteConnections": 20
  },
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://0.0.0.0:5000"
      }
    }
  }
}
```

#### 运行服务端
```bash
# 开发模式
dotnet run

# 生产部署
dotnet publish -c Release
```

### 3. 客户端部署

#### 构建客户端
```bash
cd IndustryMonitor.Client
dotnet publish -c Release --self-contained false
```

#### 配置客户端
编辑 `appsettings.json`：
```json
{
  "ServerUrl": "http://*************:5000",
  "SignalRHub": "http://*************:5000/deviceHub"
}
```

#### 运行客户端
```bash
dotnet IndustryMonitor.Client.dll
```

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────┐
│                WPF 客户端 (1-4台)                     │
│  ┌──────────┐  ┌──────────┐  ┌─────────────┐       │
│  │ 设备监控  │  │ 配方管理  │  │ 系统管理    │       │
│  └──────────┘  └──────────┘  └─────────────┘       │
└─────────────────────┬───────────────────────────────┘
                      │ HTTP / SignalR
┌─────────────────────▼───────────────────────────────┐
│               ASP.NET Core 服务端                    │
│  ┌─────────────────────────────────────────────┐    │
│  │          业务服务层                          │    │
│  │  设备通信 │ 配方管理 │ 连接池 │ 缓存服务     │    │
│  └─────────────────────────────────────────────┘    │
│  ┌──────────┐ ┌──────────┐ ┌─────────────┐         │
│  │ Redis    │ │ SQLite   │ │ 配方文件     │         │
│  └──────────┘ └──────────┘ └─────────────┘         │
└─────────────────────┬───────────────────────────────┘
                      │ Modbus TCP
┌─────────────────────▼───────────────────────────────┐
│                200台 PLC 设备                        │
└─────────────────────────────────────────────────────┘
```

## 📁 项目结构

```
IndustryMonitor/
├── IndustryMonitor.Server/     # 服务端项目
│   ├── Controllers/           # API控制器
│   ├── Services/              # 业务服务
│   ├── Hubs/                  # SignalR Hub
│   ├── Models/                # 数据模型
│   └── Data/                  # 数据访问层
├── IndustryMonitor.Client/     # WPF客户端项目
│   ├── Views/                 # MVVM视图
│   ├── ViewModels/            # 视图模型
│   ├── Services/              # 客户端服务
│   └── Controls/              # 自定义控件
├── IndustryMonitor.Shared/     # 共享模型库
└── docs/                      # 文档目录
```

## 🔧 开发指南

### 开发环境搭建
```bash
# 克隆项目
git clone https://github.com/your-org/IndustryMonitor.git

# 还原NuGet包
dotnet restore

# 构建解决方案
dotnet build

# 运行测试
dotnet test
```

### 主要技术栈

#### 服务端
- **ASP.NET Core 8.0** - Web API框架
- **SignalR** - 实时通信
- **Entity Framework Core** - ORM框架
- **SQLite** - 数据库
- **Redis** - 缓存
- **HslCommunication** - 工业通信库
- **Serilog** - 结构化日志

#### 客户端
- **WPF (.NET 8)** - 桌面应用框架
- **Prism 8.x** - MVVM框架
- **Material Design** - UI设计语言
- **LiveCharts2** - 图表控件
- **SignalR Client** - 实时通信客户端

### 调试指南

#### 服务端调试
```bash
# 启动服务端调试
cd IndustryMonitor.Server
dotnet run --environment Development

# 查看API文档 (Swagger)
# 访问: http://localhost:5000/swagger
```

#### 客户端调试
```bash
# 启动客户端调试
cd IndustryMonitor.Client
dotnet run
```

## 📊 性能指标

| 指标 | 目标值 | 实际值 |
|------|--------|--------|
| 设备采集周期 | 500ms | 495-505ms |
| 最大设备数量 | 200台 | 200台 |
| 并发连接数 | 80个 | 80个 |
| 内存使用 | < 500MB | ~300MB |
| CPU使用率 | < 20% | ~15% |
| 响应延迟 | < 100ms | ~50ms |

## 🔒 安全特性

- **JWT认证**: 基于Token的身份认证
- **角色权限**: 管理员/操作员/查看者分级权限
- **操作审计**: 完整的操作日志记录
- **数据加密**: 敏感数据加密存储
- **输入验证**: 全面的输入参数验证
- **SQL注入防护**: 参数化查询保护

## 📖 文档资源

- [📋 技术规格文档](IndustryMonitor-TechnicalDocument.md)
- [🚀 部署指南](DEPLOYMENT.md)
- [📚 API接口文档](API-REFERENCE.md)
- [👥 用户操作手册](USER-MANUAL.md)
- [🐛 故障排查指南](TROUBLESHOOTING.md)

## 🤝 贡献指南

我们欢迎社区贡献！请阅读 [贡献指南](CONTRIBUTING.md) 了解如何参与项目开发。

### 开发流程
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码规范
- 遵循 Microsoft C# 编码规范
- 使用 EditorConfig 统一代码风格
- 编写单元测试覆盖核心逻辑
- 添加 XML 文档注释

## 📝 更新日志

### [1.0.0] - 2024-01-20
- ✨ 初始版本发布
- 🎯 支持200台设备实时监控
- 📦 完整的配方管理功能
- 🚀 高性能批量下发
- 💻 Material Design客户端界面

### [0.9.0] - 2024-01-15
- 🧪 Beta版本测试
- 🐛 修复连接池内存泄漏问题
- ⚡ 优化设备读取性能
- 📱 改进客户端响应速度

## ❓ 常见问题

### Q: 支持哪些PLC型号？
A: 支持所有兼容Modbus TCP协议的PLC设备，包括西门子、三菱、欧姆龙、施耐德等主流品牌。

### Q: 可以监控多少台设备？
A: 当前版本支持200台设备，如需更大规模请联系我们定制。

### Q: 客户端支持多少个同时连接？
A: 理论上无限制，建议单个服务端连接数不超过50个客户端。

### Q: 数据保存多长时间？
A: 实时数据保存7天，历史数据可配置保存1年或更长时间。

## 📞 技术支持

- 📧 邮箱: <EMAIL>
- 💬 QQ群: *********
- 📱 微信: IndustryMonitor
- 🌐 官网: https://www.yourcompany.com

## 📄 许可证

本项目基于 [MIT License](LICENSE) 开源协议。

---

**⭐ 如果这个项目对您有帮助，请给我们一个Star！**
