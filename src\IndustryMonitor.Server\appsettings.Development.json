{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Information", "Microsoft.EntityFrameworkCore": "Information"}}, "ConnectionStrings": {"SQLite": "Data Source=industry_dev.db;Cache=Shared;", "Redis": "localhost:6379,database=1,connectTimeout=5000,syncTimeout=1000"}, "SystemSettings": {"EnableSwagger": true, "MaxConcurrentWriteTasks": 2}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "IndustryMonitor": "Debug"}}}}