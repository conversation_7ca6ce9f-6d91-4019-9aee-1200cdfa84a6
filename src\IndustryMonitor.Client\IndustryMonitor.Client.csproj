<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <AssemblyTitle>工业设备监控系统客户端</AssemblyTitle>
    <Product>IndustryMonitor Client</Product>
    <Description>工业设备监控系统桌面客户端</Description>
    <Copyright>Copyright © 2024</Copyright>
    <Version>1.0.0</Version>
        <StartupObject>IndustryMonitor.Client.App</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Prism.Wpf" Version="8.1.97" />
    <PackageReference Include="Prism.Unity" Version="8.1.97" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="System.Net.Http.Json" Version="8.0.0" />
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.77" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\IndustryMonitor.Shared\IndustryMonitor.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Views\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Services\" />
    <Folder Include="Controls\" />
    <Folder Include="Converters\" />
    <Folder Include="Resources\" />
    <Folder Include="Models\" />
    <Folder Include="Utils\" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\**\*" />
  </ItemGroup>


</Project>