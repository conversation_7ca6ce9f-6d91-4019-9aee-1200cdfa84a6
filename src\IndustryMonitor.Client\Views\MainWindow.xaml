<Window x:Class="IndustryMonitor.Client.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:IndustryMonitor.Client.Views"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="工业设备监控系统" Height="720" Width="1280"
        TextOptions.TextFormattingMode="Display"
        TextOptions.TextRenderingMode="ClearType"
        FontFamily="Microsoft YaHei UI, Segoe UI"
        Background="#F5F7FA">
    
    <Grid>
        <!-- 顶部导航栏 -->
        <Border Height="60" Background="White" ShadowDepth="2">
            <DockPanel>
                <StackPanel DockPanel.Dock="Left" Orientation="Horizontal" Margin="20,0">
                    <Border Width="32" Height="32" Background="#1976D2" CornerRadius="4">
                        <materialDesign:PackIcon Kind="Factory" Foreground="White" Width="20" Height="20"/>
                    </Border>
                    <TextBlock Text="工业设备监控系统" FontSize="16" FontWeight="Medium" Margin="12,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal" Margin="0,0,20,0">
                    <Button Style="{DynamicResource MaterialDesignIconButton}">
                        <materialDesign:PackIcon Kind="BellOutline"/>
                    </Button>
                    <Button Style="{DynamicResource MaterialDesignIconButton}">
                        <materialDesign:PackIcon Kind="DotsVertical"/>
                    </Button>
                </StackPanel>
            </DockPanel>
        </Border>

        <Grid Margin="0,60,0,0">
            <!-- 左侧导航菜单 -->
            <Border Width="200" Background="White" Margin="20,20,0,20" CornerRadius="8">
                <Border.Effect>
                    <DropShadowEffect BlurRadius="10" Opacity="0.1" Direction="270"/>
                </Border.Effect>
                
                <StackPanel Margin="0,16">
                    <Border Padding="16,12">
                        <DockPanel>
                            <materialDesign:PackIcon DockPanel.Dock="Left" Kind="Dashboard" Foreground="#1976D2" Width="20" Height="20"/>
                            <TextBlock Text="仪表板" FontSize="14" FontWeight="Medium" Margin="12,0,0,0" Foreground="#1976D2"/>
                        </DockPanel>
                    </Border>

                    <ItemsControl Margin="0,16">
                        <materialDesign:Card UniformCornerRadius="8" Margin="8,2">
                            <Button Style="{DynamicResource MaterialDesignFlatButton}" 
                                    Content="设备监控" FontSize="14" Foreground="#333"/>
                        </materialDesign:Card>
                        
                        <materialDesign:Card UniformCornerRadius="8" Margin="8,2">
                            <Button Style="{DynamicResource MaterialDesignFlatButton}}" 
                                    Content="配方管理" FontSize="14" Foreground="#333"/>
                        </materialDesign:Card>
                        
                        <materialDesign:Card UniformCornerRadius="8" Margin="8,2">
                            <Button Style="{DynamicResource MaterialDesignFlatButton}}" 
                                    Content="任务执行" FontSize="14" Foreground="#333"/>
                        </materialDesign:Card>
                    </ItemsControl>
                </StackPanel>
            </Border>

            <!-- 主内容区域 -->
            <Grid Margin="220,20,20,20">
                <ScrollViewer HorizontalScrollBarVisibility="Hidden">
                    <Grid>
                        <local:DeviceListView />
                    </Grid>
                </ScrollViewer>
            </Grid>
        </Grid>
    </Grid>
</Window>
