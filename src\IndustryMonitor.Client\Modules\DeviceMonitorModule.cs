using Prism.Ioc;
using Prism.Modularity;

namespace IndustryMonitor.Client.Modules
{
    public class DeviceMonitorModule : IModule
    {
        public void OnInitialized(IContainerProvider containerProvider)
        {
            // This method is called after the module has been registered
            // and all its dependencies have been resolved.
            // You can perform any initialization logic here.
        }

        public void RegisterTypes(IContainerRegistry containerRegistry)
        {
            // Register services and viewmodels specific to this module here.
            // For example:
            // containerRegistry.Register<IDeviceMonitorService, DeviceMonitorService>();
        }
    }
}
