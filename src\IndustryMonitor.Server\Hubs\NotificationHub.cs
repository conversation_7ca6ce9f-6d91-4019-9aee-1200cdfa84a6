using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using IndustryMonitor.Server.Data.Repositories;
using IndustryMonitor.Shared.Models.Entities;
using IndustryMonitor.Shared.Enums;

namespace IndustryMonitor.Server.Hubs;

/// <summary>
/// 通知 SignalR Hub
/// 提供系统通知和消息的实时推送
/// </summary>
[Authorize]
public class NotificationHub : Hub
{
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly ILogger<NotificationHub> _logger;

    public NotificationHub(
        IServiceScopeFactory scopeFactory,
        ILogger<NotificationHub> logger)
    {
        _scopeFactory = scopeFactory;
        _logger = logger;
    }

    public override async Task OnConnectedAsync()
    {
        var username = Context.User?.Identity?.Name ?? "Anonymous";
        var connectionId = Context.ConnectionId;
        
        _logger.LogInformation("通知客户端连接: 用户 = {Username}, 连接ID = {ConnectionId}", 
            username, connectionId);
        
        // 根据用户角色加入相应的通知组
        await JoinUserRoleGroupsAsync(username);
        
        await Clients.Caller.SendAsync("Connected", new
        {
            ConnectionId = connectionId,
            Username = username,
            ConnectedAt = DateTime.UtcNow,
            Message = "已连接到通知中心"
        });
        
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var username = Context.User?.Identity?.Name ?? "Anonymous";
        var connectionId = Context.ConnectionId;
        
        _logger.LogInformation("通知客户端断开连接: 用户 = {Username}, 连接ID = {ConnectionId}, 原因 = {Reason}", 
            username, connectionId, exception?.Message ?? "正常断开");
        
        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// 发送个人消息
    /// </summary>
    [Authorize(Roles = "Admin,Operator")]
    public async Task SendPrivateMessage(string targetUsername, string message)
    {
        try
        {
            var senderUsername = Context.User?.Identity?.Name ?? "Anonymous";
            
            var notification = new
            {
                Type = "PrivateMessage",
                From = senderUsername,
                To = targetUsername,
                Message = message,
                Timestamp = DateTime.UtcNow
            };
            
            // 发送给目标用户
            await Clients.Group($"User_{targetUsername}").SendAsync("PrivateMessage", notification);
            
            // 确认消息已发送
            await Clients.Caller.SendAsync("MessageSent", new
            {
                To = targetUsername,
                Message = message,
                SentAt = DateTime.UtcNow
            });
            
            _logger.LogInformation("私人消息已发送: 从 {From} 到 {To}", senderUsername, targetUsername);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送私人消息失败");
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "发送私人消息失败",
                Details = ex.Message
            });
        }
    }

    /// <summary>
    /// 发送广播消息
    /// </summary>
    [Authorize(Roles = "Admin")]
    public async Task SendBroadcastMessage(string message, string? priority = "Normal")
    {
        try
        {
            var senderUsername = Context.User?.Identity?.Name ?? "Admin";
            
            var notification = new
            {
                Type = "BroadcastMessage",
                From = senderUsername,
                Message = message,
                Priority = priority,
                Timestamp = DateTime.UtcNow
            };
            
            // 发送给所有连接的用户
            await Clients.All.SendAsync("BroadcastMessage", notification);
            
            // 记录操作日志
            await LogOperationAsync("发送系统广播", $"消息: {message}");
            
            _logger.LogInformation("系统广播消息已发送: {Message}, 发送者: {Sender}", message, senderUsername);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送广播消息失败");
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "发送广播消息失败",
                Details = ex.Message
            });
        }
    }

    /// <summary>
    /// 发送角色组消息
    /// </summary>
    [Authorize(Roles = "Admin,Operator")]
    public async Task SendRoleMessage(string targetRole, string message)
    {
        try
        {
            var senderUsername = Context.User?.Identity?.Name ?? "Anonymous";
            
            if (!IsValidRole(targetRole))
            {
                await Clients.Caller.SendAsync("Error", new
                {
                    Message = "无效的目标角色",
                    Details = $"角色 '{targetRole}' 不存在"
                });
                return;
            }
            
            var notification = new
            {
                Type = "RoleMessage",
                From = senderUsername,
                To = targetRole,
                Message = message,
                Timestamp = DateTime.UtcNow
            };
            
            // 发送给指定角色组
            await Clients.Group($"Role_{targetRole}").SendAsync("RoleMessage", notification);
            
            await Clients.Caller.SendAsync("MessageSent", new
            {
                To = $"Role_{targetRole}",
                Message = message,
                SentAt = DateTime.UtcNow
            });
            
            _logger.LogInformation("角色消息已发送: 从 {From} 到角色 {Role}", senderUsername, targetRole);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送角色消息失败");
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "发送角色消息失败",
                Details = ex.Message
            });
        }
    }

    /// <summary>
    /// 加入特定通知组
    /// </summary>
    public async Task JoinNotificationGroup(string groupName)
    {
        try
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
            
            var username = Context.User?.Identity?.Name ?? "Anonymous";
            _logger.LogInformation("用户 {Username} 加入通知组: {GroupName}", username, groupName);
            
            await Clients.Caller.SendAsync("JoinedNotificationGroup", new
            {
                GroupName = groupName,
                JoinedAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加入通知组失败: GroupName = {GroupName}", groupName);
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "加入通知组失败",
                Details = ex.Message
            });
        }
    }

    /// <summary>
    /// 离开特定通知组
    /// </summary>
    public async Task LeaveNotificationGroup(string groupName)
    {
        try
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
            
            var username = Context.User?.Identity?.Name ?? "Anonymous";
            _logger.LogInformation("用户 {Username} 离开通知组: {GroupName}", username, groupName);
            
            await Clients.Caller.SendAsync("LeftNotificationGroup", new
            {
                GroupName = groupName,
                LeftAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "离开通知组失败: GroupName = {GroupName}", groupName);
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "离开通知组失败",
                Details = ex.Message
            });
        }
    }

    /// <summary>
    /// 获取未读通知数量
    /// </summary>
    public async Task GetUnreadNotificationCount()
    {
        try
        {
            var username = Context.User?.Identity?.Name;
            if (string.IsNullOrEmpty(username))
            {
                await Clients.Caller.SendAsync("UnreadNotificationCount", new { Count = 0 });
                return;
            }
            
            // 这里应该从数据库查询实际的未读通知数量
            // 暂时返回模拟数据
            var unreadCount = 0; // 实际实现中应该查询数据库
            
            await Clients.Caller.SendAsync("UnreadNotificationCount", new
            {
                Count = unreadCount,
                Username = username,
                CheckedAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取未读通知数量失败");
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "获取未读通知数量失败",
                Details = ex.Message
            });
        }
    }

    private async Task JoinUserRoleGroupsAsync(string username)
    {
        try
        {
            using var scope = _scopeFactory.CreateScope();
            var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
            
            var user = await unitOfWork.Users.GetByUsernameAsync(username);
            if (user != null)
            {
                // 加入用户专属组
                await Groups.AddToGroupAsync(Context.ConnectionId, $"User_{username}");
                
                // 加入角色组
                await Groups.AddToGroupAsync(Context.ConnectionId, $"Role_{user.Role}");
                
                _logger.LogInformation("用户 {Username} 已加入角色组: {Role}", username, user.Role);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加入用户角色组失败: Username = {Username}", username);
        }
    }

    private async Task LogOperationAsync(string operation, string details)
    {
        try
        {
            var username = Context.User?.Identity?.Name;
            if (string.IsNullOrEmpty(username))
                return;
            
            using var scope = _scopeFactory.CreateScope();
            var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
            
            var user = await unitOfWork.Users.GetByUsernameAsync(username);
            if (user != null)
            {
                var log = new OperationLog
                {
                    UserId = user.Id,
                    Operation = OperationType.SystemOperation,
                    Details = $"{operation}: {details}",
                    IpAddress = Context.GetHttpContext()?.Connection?.RemoteIpAddress?.ToString() ?? "Unknown",
                    UserAgent = Context.GetHttpContext()?.Request?.Headers["User-Agent"].ToString() ?? "Unknown",
                    CreatedAt = DateTime.UtcNow
                };
                
                await unitOfWork.OperationLogs.AddAsync(log);
                await unitOfWork.SaveChangesAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录操作日志失败");
        }
    }

    private static bool IsValidRole(string role)
    {
        return Enum.TryParse<UserRole>(role, true, out _);
    }
}