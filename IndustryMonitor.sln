﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IndustryMonitor.Server", "src\IndustryMonitor.Server\IndustryMonitor.Server.csproj", "{A8B2E9C1-2D3F-4E5F-8A9B-1C2D3E4F5678}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IndustryMonitor.Client", "src\IndustryMonitor.Client\IndustryMonitor.Client.csproj", "{B9C3F0D2-3E4F-5F6A-9BAC-2D3E4F567890}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IndustryMonitor.Shared", "src\IndustryMonitor.Shared\IndustryMonitor.Shared.csproj", "{C0D4E1F3-4F5A-6A7B-ACBD-3E4F56789012}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{D1E5F2A4-5A6B-7B8C-BDCE-4F567890123A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{E2F6A3B5-6B7C-8C9D-CEDF-567890123ABC}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{F3A7B4C6-7C8D-9D0E-DFEA-67890123ABCD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "IndustryMonitor.Tests", "tests\IndustryMonitor.Tests\IndustryMonitor.Tests.csproj", "{A4B8C5D7-8D9E-0E1F-E0FB-7890123ABCDE}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A8B2E9C1-2D3F-4E5F-8A9B-1C2D3E4F5678}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A8B2E9C1-2D3F-4E5F-8A9B-1C2D3E4F5678}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A8B2E9C1-2D3F-4E5F-8A9B-1C2D3E4F5678}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A8B2E9C1-2D3F-4E5F-8A9B-1C2D3E4F5678}.Debug|x64.Build.0 = Debug|Any CPU
		{A8B2E9C1-2D3F-4E5F-8A9B-1C2D3E4F5678}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A8B2E9C1-2D3F-4E5F-8A9B-1C2D3E4F5678}.Debug|x86.Build.0 = Debug|Any CPU
		{A8B2E9C1-2D3F-4E5F-8A9B-1C2D3E4F5678}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A8B2E9C1-2D3F-4E5F-8A9B-1C2D3E4F5678}.Release|Any CPU.Build.0 = Release|Any CPU
		{A8B2E9C1-2D3F-4E5F-8A9B-1C2D3E4F5678}.Release|x64.ActiveCfg = Release|Any CPU
		{A8B2E9C1-2D3F-4E5F-8A9B-1C2D3E4F5678}.Release|x64.Build.0 = Release|Any CPU
		{A8B2E9C1-2D3F-4E5F-8A9B-1C2D3E4F5678}.Release|x86.ActiveCfg = Release|Any CPU
		{A8B2E9C1-2D3F-4E5F-8A9B-1C2D3E4F5678}.Release|x86.Build.0 = Release|Any CPU
		{B9C3F0D2-3E4F-5F6A-9BAC-2D3E4F567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B9C3F0D2-3E4F-5F6A-9BAC-2D3E4F567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B9C3F0D2-3E4F-5F6A-9BAC-2D3E4F567890}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B9C3F0D2-3E4F-5F6A-9BAC-2D3E4F567890}.Debug|x64.Build.0 = Debug|Any CPU
		{B9C3F0D2-3E4F-5F6A-9BAC-2D3E4F567890}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B9C3F0D2-3E4F-5F6A-9BAC-2D3E4F567890}.Debug|x86.Build.0 = Debug|Any CPU
		{B9C3F0D2-3E4F-5F6A-9BAC-2D3E4F567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B9C3F0D2-3E4F-5F6A-9BAC-2D3E4F567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{B9C3F0D2-3E4F-5F6A-9BAC-2D3E4F567890}.Release|x64.ActiveCfg = Release|Any CPU
		{B9C3F0D2-3E4F-5F6A-9BAC-2D3E4F567890}.Release|x64.Build.0 = Release|Any CPU
		{B9C3F0D2-3E4F-5F6A-9BAC-2D3E4F567890}.Release|x86.ActiveCfg = Release|Any CPU
		{B9C3F0D2-3E4F-5F6A-9BAC-2D3E4F567890}.Release|x86.Build.0 = Release|Any CPU
		{C0D4E1F3-4F5A-6A7B-ACBD-3E4F56789012}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C0D4E1F3-4F5A-6A7B-ACBD-3E4F56789012}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C0D4E1F3-4F5A-6A7B-ACBD-3E4F56789012}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C0D4E1F3-4F5A-6A7B-ACBD-3E4F56789012}.Debug|x64.Build.0 = Debug|Any CPU
		{C0D4E1F3-4F5A-6A7B-ACBD-3E4F56789012}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C0D4E1F3-4F5A-6A7B-ACBD-3E4F56789012}.Debug|x86.Build.0 = Debug|Any CPU
		{C0D4E1F3-4F5A-6A7B-ACBD-3E4F56789012}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C0D4E1F3-4F5A-6A7B-ACBD-3E4F56789012}.Release|Any CPU.Build.0 = Release|Any CPU
		{C0D4E1F3-4F5A-6A7B-ACBD-3E4F56789012}.Release|x64.ActiveCfg = Release|Any CPU
		{C0D4E1F3-4F5A-6A7B-ACBD-3E4F56789012}.Release|x64.Build.0 = Release|Any CPU
		{C0D4E1F3-4F5A-6A7B-ACBD-3E4F56789012}.Release|x86.ActiveCfg = Release|Any CPU
		{C0D4E1F3-4F5A-6A7B-ACBD-3E4F56789012}.Release|x86.Build.0 = Release|Any CPU
		{A4B8C5D7-8D9E-0E1F-E0FB-7890123ABCDE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A4B8C5D7-8D9E-0E1F-E0FB-7890123ABCDE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A4B8C5D7-8D9E-0E1F-E0FB-7890123ABCDE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A4B8C5D7-8D9E-0E1F-E0FB-7890123ABCDE}.Debug|x64.Build.0 = Debug|Any CPU
		{A4B8C5D7-8D9E-0E1F-E0FB-7890123ABCDE}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A4B8C5D7-8D9E-0E1F-E0FB-7890123ABCDE}.Debug|x86.Build.0 = Debug|Any CPU
		{A4B8C5D7-8D9E-0E1F-E0FB-7890123ABCDE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A4B8C5D7-8D9E-0E1F-E0FB-7890123ABCDE}.Release|Any CPU.Build.0 = Release|Any CPU
		{A4B8C5D7-8D9E-0E1F-E0FB-7890123ABCDE}.Release|x64.ActiveCfg = Release|Any CPU
		{A4B8C5D7-8D9E-0E1F-E0FB-7890123ABCDE}.Release|x64.Build.0 = Release|Any CPU
		{A4B8C5D7-8D9E-0E1F-E0FB-7890123ABCDE}.Release|x86.ActiveCfg = Release|Any CPU
		{A4B8C5D7-8D9E-0E1F-E0FB-7890123ABCDE}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A8B2E9C1-2D3F-4E5F-8A9B-1C2D3E4F5678} = {D1E5F2A4-5A6B-7B8C-BDCE-4F567890123A}
		{B9C3F0D2-3E4F-5F6A-9BAC-2D3E4F567890} = {D1E5F2A4-5A6B-7B8C-BDCE-4F567890123A}
		{C0D4E1F3-4F5A-6A7B-ACBD-3E4F56789012} = {D1E5F2A4-5A6B-7B8C-BDCE-4F567890123A}
		{A4B8C5D7-8D9E-0E1F-E0FB-7890123ABCDE} = {E2F6A3B5-6B7C-8C9D-CEDF-567890123ABC}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-1234-1234-123456789012}
	EndGlobalSection
EndGlobal
