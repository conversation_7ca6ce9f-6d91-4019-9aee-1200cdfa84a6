{"format": 1, "restore": {"D:\\Cursor-test\\industry\\src\\IndustryMonitor.Client\\IndustryMonitor.Client.csproj": {}}, "projects": {"D:\\Cursor-test\\industry\\src\\IndustryMonitor.Client\\IndustryMonitor.Client.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Cursor-test\\industry\\src\\IndustryMonitor.Client\\IndustryMonitor.Client.csproj", "projectName": "IndustryMonitor.Client", "projectPath": "D:\\Cursor-test\\industry\\src\\IndustryMonitor.Client\\IndustryMonitor.Client.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Cursor-test\\industry\\src\\IndustryMonitor.Client\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "D:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"D:\\Cursor-test\\industry\\src\\IndustryMonitor.Shared\\IndustryMonitor.Shared.csproj": {"projectPath": "D:\\Cursor-test\\industry\\src\\IndustryMonitor.Shared\\IndustryMonitor.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "MaterialDesignColors": {"target": "Package", "version": "[2.1.4, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.AspNetCore.SignalR.Client": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Xaml.Behaviors.Wpf": {"target": "Package", "version": "[1.1.77, )"}, "Prism.Unity": {"target": "Package", "version": "[8.1.97, )"}, "Prism.Wpf": {"target": "Package", "version": "[8.1.97, )"}, "System.Net.Http.Json": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Cursor-test\\industry\\src\\IndustryMonitor.Shared\\IndustryMonitor.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Cursor-test\\industry\\src\\IndustryMonitor.Shared\\IndustryMonitor.Shared.csproj", "projectName": "IndustryMonitor.Shared", "projectPath": "D:\\Cursor-test\\industry\\src\\IndustryMonitor.Shared\\IndustryMonitor.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Cursor-test\\industry\\src\\IndustryMonitor.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "D:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}