namespace IndustryMonitor.Shared.DTOs.Requests;

/// <summary>
/// 登录请求
/// </summary>
public class LoginRequest
{
    public string Username { get; set; } = "";
    public string Password { get; set; } = "";
}

/// <summary>
/// 刷新Token请求
/// </summary>
public class RefreshTokenRequest
{
    public string RefreshToken { get; set; } = "";
}

/// <summary>
/// 修改密码请求
/// </summary>
public class ChangePasswordRequest
{
    public string CurrentPassword { get; set; } = "";
    public string NewPassword { get; set; } = "";
}

/// <summary>
/// 创建设备请求
/// </summary>
public class CreateDeviceRequest
{
    public string Name { get; set; } = "";
    public string IpAddress { get; set; } = "";
    public int Port { get; set; } = 502;
    public int SlaveId { get; set; } = 1;
    public int RegisterStart { get; set; }
    public int RegisterCount { get; set; }
    public string? DeviceType { get; set; }
    public string? Location { get; set; }
    public string? Description { get; set; }
}

/// <summary>
/// 更新设备请求
/// </summary>
public class UpdateDeviceRequest
{
    public string? Name { get; set; }
    public string? Location { get; set; }
    public int? RegisterCount { get; set; }
    public string? Description { get; set; }
    public bool? IsActive { get; set; }
}

/// <summary>
/// 批量设备操作请求
/// </summary>
public class BatchDeviceRequest
{
    public List<int> DeviceIds { get; set; } = new();
    public string Action { get; set; } = ""; // enable, disable
}

/// <summary>
/// 创建配方请求
/// </summary>
public class CreateRecipeRequest
{
    public string Name { get; set; } = "";
    public string? Description { get; set; }
    public string Version { get; set; } = "1.0";
    public RecipeContent Content { get; set; } = new();
}

/// <summary>
/// 更新配方请求
/// </summary>
public class UpdateRecipeRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? Version { get; set; }
    public RecipeContent? Content { get; set; }
}

/// <summary>
/// 复制配方请求
/// </summary>
public class CopyRecipeRequest
{
    public string Name { get; set; } = "";
    public string? Description { get; set; }
}

/// <summary>
/// 执行配方请求
/// </summary>
public class ExecuteRecipeRequest
{
    public int RecipeId { get; set; }
    public List<int> DeviceIds { get; set; } = new();
    public string? Description { get; set; }
    public string Priority { get; set; } = "Normal"; // Low, Normal, High
    public int? TimeoutSeconds { get; set; }
    public string UserId { get; set; } = "";
}

/// <summary>
/// 重试任务请求
/// </summary>
public class RetryTaskRequest
{
    public List<int>? DeviceIds { get; set; }
    public string? Description { get; set; }
}

/// <summary>
/// 创建用户请求
/// </summary>
public class CreateUserRequest
{
    public string Username { get; set; } = "";
    public string Password { get; set; } = "";
    public string? FullName { get; set; }
    public UserRole Role { get; set; }
}

/// <summary>
/// 更新用户请求
/// </summary>
public class UpdateUserRequest
{
    public string? FullName { get; set; }
    public UserRole? Role { get; set; }
    public bool? IsActive { get; set; }
}

/// <summary>
/// 系统备份请求
/// </summary>
public class SystemBackupRequest
{
    public bool IncludeHistoryData { get; set; } = true;
    public string? Description { get; set; }
}

/// <summary>
/// 更新系统配置请求
/// </summary>
public class UpdateSystemConfigRequest
{
    public ModbusConfig? Modbus { get; set; }
    public IndustryMonitor.Shared.Models.Configs.SystemConfig? System { get; set; }
    public Dictionary<string, object>? CustomSettings { get; set; }
}