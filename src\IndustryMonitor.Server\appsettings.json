{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Information"}}, "AllowedHosts": "*", "Kestrel": {"Endpoints": {"Http": {"Url": "http://localhost:5000"}}, "Limits": {"MaxConcurrentConnections": 100, "MaxRequestBodySize": 10485760}}, "ConnectionStrings": {"SQLite": "Data Source=industry.db;Cache=Shared;", "Redis": "localhost:6379,database=0,connectTimeout=5000,syncTimeout=1000"}, "ModbusSettings": {"MaxReadConnections": 80, "MaxWriteConnections": 20, "ConnectionTimeout": 3000, "ReadTimeout": 200, "WriteTimeout": 5000, "IdleTimeoutMinutes": 5, "RetryCount": 3, "RetryDelayMs": 100}, "RedisSettings": {"ConnectionString": "localhost:6379", "Database": 0, "KeyPrefix": "IndustryMonitor:", "DefaultExpiry": "00:05:00"}, "JwtSettings": {"Secret": "YourJwtSecretKeyMustBeAtLeast32CharactersLong!", "Issuer": "IndustryMonitor.Server", "Audience": "IndustryMonitor.Client", "ExpiryDays": 7, "RefreshTokenExpiryDays": 30}, "SystemSettings": {"MaxDeviceCount": 200, "PollingIntervalMs": 500, "DataRetentionDays": 30, "MaxConcurrentWriteTasks": 5, "EnableHealthCheck": true, "EnableSwagger": true}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "File", "Args": {"path": "Logs/app-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "fileSizeLimitBytes": 104857600, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:HH:mm:ss} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}}