using Microsoft.Extensions.Options;
using IndustryMonitor.Shared.Models.Configs;
using System.Collections.Concurrent;

namespace IndustryMonitor.Server.Services.Device;

/// <summary>
/// Modbus 连接池服务实现
/// </summary>
public class ConnectionPoolService : IConnectionPool, IDisposable
{
    private readonly ModbusConfig _config;
    private readonly ILogger<ConnectionPoolService> _logger;
    private readonly IServiceProvider _serviceProvider;
    
    private readonly ConcurrentDictionary<string, ConcurrentQueue<PooledConnection>> _readPools = new();
    private readonly ConcurrentDictionary<string, ConcurrentQueue<PooledConnection>> _writePools = new();
    private readonly ConcurrentDictionary<string, PooledConnection> _activeConnections = new();
    
    private readonly Timer _cleanupTimer;
    private volatile bool _disposed = false;
    private readonly object _lockObject = new object();

    public ConnectionPoolService(
        IOptions<ModbusConfig> config, 
        ILogger<ConnectionPoolService> logger,
        IServiceProvider serviceProvider)
    {
        _config = config.Value;
        _logger = logger;
        _serviceProvider = serviceProvider;
        
        // 启动连接清理定时器
        _cleanupTimer = new Timer(CleanupIdleConnections, null, 
            TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        
        _logger.LogInformation("连接池服务已启动，读连接池大小: {ReadPoolSize}，写连接池大小: {WritePoolSize}", 
            _config.ReadConnectionPoolSize, _config.WriteConnectionPoolSize);
    }

    public async Task<IModbusConnection> GetReadConnectionAsync(string ipAddress)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(ConnectionPoolService));

        var pool = _readPools.GetOrAdd(ipAddress, _ => new ConcurrentQueue<PooledConnection>());
        
        // 尝试从池中获取可用连接
        PooledConnection? pooledConnection = null;
        while (pool.TryDequeue(out var candidate))
        {
            if (candidate.IsAlive && !candidate.InUse)
            {
                pooledConnection = candidate;
                break;
            }
            else
            {
                // 清理无效连接
                try
                {
                    candidate.Connection?.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "清理无效读连接时出错: {IpAddress}", ipAddress);
                }
            }
        }

        // 如果没有可用连接，创建新连接
        if (pooledConnection == null)
        {
            var currentPoolSize = GetPoolSize(ipAddress, "Read");
            if (currentPoolSize >= _config.ReadConnectionPoolSize)
            {
                // 池已满，等待或抛出异常
                throw new InvalidOperationException($"读连接池已满，无法为 {ipAddress} 创建更多连接");
            }

            pooledConnection = await CreatePooledConnectionAsync(ipAddress, "Read");
        }

        pooledConnection.InUse = true;
        _activeConnections[pooledConnection.Id] = pooledConnection;
        
        _logger.LogTrace("获取读连接: {IpAddress}, 连接ID: {ConnectionId}", ipAddress, pooledConnection.Id);
        
        return new PooledConnectionWrapper(pooledConnection, this);
    }

    public async Task<IModbusConnection> GetWriteConnectionAsync(string ipAddress)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(ConnectionPoolService));

        var pool = _writePools.GetOrAdd(ipAddress, _ => new ConcurrentQueue<PooledConnection>());
        
        // 尝试从池中获取可用连接
        PooledConnection? pooledConnection = null;
        while (pool.TryDequeue(out var candidate))
        {
            if (candidate.IsAlive && !candidate.InUse)
            {
                pooledConnection = candidate;
                break;
            }
            else
            {
                // 清理无效连接
                try
                {
                    candidate.Connection?.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "清理无效写连接时出错: {IpAddress}", ipAddress);
                }
            }
        }

        // 如果没有可用连接，创建新连接
        if (pooledConnection == null)
        {
            var currentPoolSize = GetPoolSize(ipAddress, "Write");
            if (currentPoolSize >= _config.WriteConnectionPoolSize)
            {
                // 池已满，等待或抛出异常
                throw new InvalidOperationException($"写连接池已满，无法为 {ipAddress} 创建更多连接");
            }

            pooledConnection = await CreatePooledConnectionAsync(ipAddress, "Write");
        }

        pooledConnection.InUse = true;
        _activeConnections[pooledConnection.Id] = pooledConnection;
        
        _logger.LogTrace("获取写连接: {IpAddress}, 连接ID: {ConnectionId}", ipAddress, pooledConnection.Id);
        
        return new PooledConnectionWrapper(pooledConnection, this);
    }

    public void ReturnConnection(IModbusConnection connection)
    {
        if (_disposed || connection == null)
            return;

        // 查找对应的池化连接
        var pooledConnection = _activeConnections.Values
            .FirstOrDefault(pc => ReferenceEquals(pc.Connection, connection));
        
        if (pooledConnection != null)
        {
            lock (_lockObject)
            {
                pooledConnection.InUse = false;
                _activeConnections.TryRemove(pooledConnection.Id, out _);

                // 检查连接是否仍然有效
                if (pooledConnection.IsAlive)
                {
                    // 返回到对应的连接池
                    var pool = pooledConnection.PoolType == "Read" 
                        ? _readPools.GetOrAdd(pooledConnection.IpAddress, _ => new ConcurrentQueue<PooledConnection>())
                        : _writePools.GetOrAdd(pooledConnection.IpAddress, _ => new ConcurrentQueue<PooledConnection>());
                    
                    pool.Enqueue(pooledConnection);
                    
                    _logger.LogTrace("返回连接到池: {IpAddress}, 类型: {PoolType}, 连接ID: {ConnectionId}", 
                        pooledConnection.IpAddress, pooledConnection.PoolType, pooledConnection.Id);
                }
                else
                {
                    // 连接无效，直接释放
                    try
                    {
                        pooledConnection.Connection?.Dispose();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "释放无效连接时出错: {IpAddress}", pooledConnection.IpAddress);
                    }
                }
            }
        }
    }

    public async Task<ConnectionPoolStatistics> GetStatisticsAsync()
    {
        var stats = new ConnectionPoolStatistics();
        var connectionsByIp = new Dictionary<string, int>();

        await Task.Run(() =>
        {
            // 统计读连接
            foreach (var kvp in _readPools)
            {
                var count = kvp.Value.Count;
                stats.ReadConnections += count;
                connectionsByIp[kvp.Key] = connectionsByIp.GetValueOrDefault(kvp.Key, 0) + count;
            }

            // 统计写连接
            foreach (var kvp in _writePools)
            {
                var count = kvp.Value.Count;
                stats.WriteConnections += count;
                connectionsByIp[kvp.Key] = connectionsByIp.GetValueOrDefault(kvp.Key, 0) + count;
            }

            // 统计活跃连接
            stats.ActiveConnections = _activeConnections.Count;
            
            // 计算总连接数
            stats.TotalConnections = stats.ReadConnections + stats.WriteConnections + stats.ActiveConnections;
            stats.IdleConnections = stats.ReadConnections + stats.WriteConnections;
            stats.ConnectionsByIp = connectionsByIp;
        });

        return stats;
    }

    public async Task CleanupIdleConnectionsAsync()
    {
        if (_disposed)
            return;

        await Task.Run(() => CleanupIdleConnections(null));
    }

    private async Task<PooledConnection> CreatePooledConnectionAsync(string ipAddress, string poolType)
    {
        try
        {
            var logger = _serviceProvider.GetRequiredService<ILogger<ModbusConnection>>();
            var configOptions = _serviceProvider.GetRequiredService<IOptions<ModbusConfig>>();
            
            var connection = new ModbusConnection(ipAddress, _config.Port, configOptions, logger);
            
            // 尝试连接
            var connected = await connection.ConnectAsync(_config.ConnectTimeout);
            if (!connected)
            {
                connection.Dispose();
                throw new InvalidOperationException($"无法连接到 Modbus 设备: {ipAddress}:{_config.Port}");
            }

            var pooledConnection = new PooledConnection
            {
                Id = Guid.NewGuid().ToString(),
                IpAddress = ipAddress,
                Connection = connection,
                CreatedAt = DateTime.UtcNow,
                InUse = false,
                PoolType = poolType
            };

            _logger.LogDebug("创建新的 {PoolType} 连接: {IpAddress}, 连接ID: {ConnectionId}", 
                poolType, ipAddress, pooledConnection.Id);

            return pooledConnection;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建 {PoolType} 连接失败: {IpAddress}", poolType, ipAddress);
            throw;
        }
    }

    private int GetPoolSize(string ipAddress, string poolType)
    {
        var pools = poolType == "Read" ? _readPools : _writePools;
        if (pools.TryGetValue(ipAddress, out var pool))
        {
            return pool.Count;
        }
        return 0;
    }

    private void CleanupIdleConnections(object? state)
    {
        if (_disposed)
            return;

        try
        {
            var cutoffTime = DateTime.UtcNow.AddMinutes(-_config.ConnectionIdleTimeout);
            var cleanedCount = 0;

            // 清理读连接池
            cleanedCount += CleanupPoolConnections(_readPools, cutoffTime, "Read");
            
            // 清理写连接池
            cleanedCount += CleanupPoolConnections(_writePools, cutoffTime, "Write");

            if (cleanedCount > 0)
            {
                _logger.LogInformation("连接池清理完成，清理了 {CleanedCount} 个空闲连接", cleanedCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "连接池清理过程中出现错误");
        }
    }

    private int CleanupPoolConnections(
        ConcurrentDictionary<string, ConcurrentQueue<PooledConnection>> pools, 
        DateTime cutoffTime, 
        string poolType)
    {
        var cleanedCount = 0;

        foreach (var kvp in pools.ToList())
        {
            var ipAddress = kvp.Key;
            var pool = kvp.Value;
            var tempList = new List<PooledConnection>();

            // 检查池中的连接
            while (pool.TryDequeue(out var connection))
            {
                if (connection.IsAlive && 
                    connection.Connection.LastUsed > cutoffTime && 
                    !connection.InUse)
                {
                    // 连接仍然有效且未超时
                    tempList.Add(connection);
                }
                else
                {
                    // 连接无效或已超时，释放它
                    try
                    {
                        connection.Connection?.Dispose();
                        cleanedCount++;
                        
                        _logger.LogTrace("清理 {PoolType} 连接: {IpAddress}, 连接ID: {ConnectionId}", 
                            poolType, ipAddress, connection.Id);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "清理连接时出错: {IpAddress}, 连接ID: {ConnectionId}", 
                            ipAddress, connection.Id);
                    }
                }
            }

            // 将有效连接放回池中
            foreach (var connection in tempList)
            {
                pool.Enqueue(connection);
            }

            // 如果池为空，移除它
            if (pool.IsEmpty)
            {
                pools.TryRemove(ipAddress, out _);
            }
        }

        return cleanedCount;
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            
            try
            {
                _cleanupTimer?.Dispose();

                // 释放所有活跃连接
                foreach (var connection in _activeConnections.Values)
                {
                    try
                    {
                        connection.Connection?.Dispose();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "释放活跃连接时出错: {ConnectionId}", connection.Id);
                    }
                }
                _activeConnections.Clear();

                // 释放读连接池
                foreach (var pool in _readPools.Values)
                {
                    while (pool.TryDequeue(out var connection))
                    {
                        try
                        {
                            connection.Connection?.Dispose();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "释放读连接池连接时出错: {ConnectionId}", connection.Id);
                        }
                    }
                }
                _readPools.Clear();

                // 释放写连接池
                foreach (var pool in _writePools.Values)
                {
                    while (pool.TryDequeue(out var connection))
                    {
                        try
                        {
                            connection.Connection?.Dispose();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "释放写连接池连接时出错: {ConnectionId}", connection.Id);
                        }
                    }
                }
                _writePools.Clear();

                _logger.LogInformation("连接池服务已释放所有资源");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放连接池服务资源时出现错误");
            }
        }
    }
}