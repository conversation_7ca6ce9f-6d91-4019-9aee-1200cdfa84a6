using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using IndustryMonitor.Shared.Enums;

namespace IndustryMonitor.Shared.Models.Entities;

/// <summary>
/// 用户实体
/// </summary>
[Table("Users")]
public class User
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }
    
    [Required]
    [StringLength(50)]
    public string Username { get; set; } = "";
    
    [Required]
    [StringLength(255)]
    public string PasswordHash { get; set; } = "";
    
    [StringLength(100)]
    public string? FullName { get; set; }
    
    [Required]
    public UserRole Role { get; set; } = UserRole.Viewer;
    
    public bool IsActive { get; set; } = true;
    
    public DateTime? LastLoginAt { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    // 导航属性
    public virtual ICollection<OperationLog> OperationLogs { get; set; } = new List<OperationLog>();
    public virtual ICollection<Recipe> CreatedRecipes { get; set; } = new List<Recipe>();
    public virtual ICollection<WriteTask> WriteTasks { get; set; } = new List<WriteTask>();
}

/// <summary>
/// 设备实体
/// </summary>
[Table("Devices")]
public class Device
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }
    
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = "";
    
    [Required]
    [StringLength(15)]
    public string IpAddress { get; set; } = "";
    
    public int Port { get; set; } = 502;
    
    public int SlaveId { get; set; } = 1;
    
    public int RegisterStart { get; set; }
    
    public int RegisterCount { get; set; }
    
    [StringLength(50)]
    public string? DeviceType { get; set; }
    
    [StringLength(100)]
    public string? Location { get; set; }
    
    [StringLength(500)]
    public string? Description { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    // 导航属性
    public virtual ICollection<DeviceHistory> DeviceHistories { get; set; } = new List<DeviceHistory>();
}

/// <summary>
/// 配方实体
/// </summary>
[Table("Recipes")]
public class Recipe
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }
    
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = "";
    
    [StringLength(1000)]
    public string? Description { get; set; }
    
    [Required]
    public string Content { get; set; } = ""; // JSON格式的配方内容
    
    [StringLength(20)]
    public string Version { get; set; } = "1.0";
    
    public bool IsLocked { get; set; } = false;
    
    [StringLength(50)]
    public string? LockedBy { get; set; }
    
    public DateTime? LockedAt { get; set; }
    
    [StringLength(50)]
    public string? CreatedBy { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    [StringLength(50)]
    public string? UpdatedBy { get; set; }
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    // 外键
    public int? CreatedByUserId { get; set; }
    public int? UpdatedByUserId { get; set; }
    
    // 导航属性
    [ForeignKey("CreatedByUserId")]
    public virtual User? CreatedByUser { get; set; }
    
    [ForeignKey("UpdatedByUserId")]
    public virtual User? UpdatedByUser { get; set; }
    
    public virtual ICollection<WriteTask> WriteTasks { get; set; } = new List<WriteTask>();
    public virtual ICollection<RecipeHistory> RecipeHistories { get; set; } = new List<RecipeHistory>();
}

/// <summary>
/// 写入任务实体
/// </summary>
[Table("WriteTasks")]
public class WriteTask
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }
    
    [Required]
    [StringLength(50)]
    public string TaskId { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public int RecipeId { get; set; }
    
    [Required]
    public string DeviceIds { get; set; } = ""; // JSON数组格式
    
    public WriteTaskStatus Status { get; set; } = WriteTaskStatus.Pending;
    
    public double Progress { get; set; } = 0;
    
    public DateTime? StartedAt { get; set; }
    
    public DateTime? CompletedAt { get; set; }
    
    [StringLength(1000)]
    public string? Error { get; set; }
    
    [StringLength(50)]
    public string? CreatedBy { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    // 外键
    public int? CreatedByUserId { get; set; }
    
    // 导航属性
    [ForeignKey("RecipeId")]
    public virtual Recipe Recipe { get; set; } = null!;
    
    [ForeignKey("CreatedByUserId")]
    public virtual User? CreatedByUser { get; set; }
}

/// <summary>
/// 操作日志实体
/// </summary>
[Table("OperationLogs")]
public class OperationLog
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }
    
    public int? UserId { get; set; }
    
    [StringLength(50)]
    public string? UserName { get; set; }
    
    [Required]
    public OperationType Operation { get; set; }
    
    [StringLength(200)]
    public string? Target { get; set; }
    
    [StringLength(1000)]
    public string? Details { get; set; }
    
    [StringLength(50)]
    public string? IpAddress { get; set; }
    
    [StringLength(20)]
    public string Result { get; set; } = "Success";
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    // 外键
    [ForeignKey("UserId")]
    public virtual User? User { get; set; }
}

/// <summary>
/// 设备历史数据实体
/// </summary>
[Table("DeviceHistories")]
public class DeviceHistory
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }
    
    [Required]
    public int DeviceId { get; set; }
    
    [Required]
    public string Data { get; set; } = ""; // JSON格式的寄存器数据
    
    public DataQuality Quality { get; set; } = DataQuality.Good;
    
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    // 外键
    [ForeignKey("DeviceId")]
    public virtual Device Device { get; set; } = null!;
}

/// <summary>
/// 配方历史记录实体
/// </summary>
[Table("RecipeHistories")]
public class RecipeHistory
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }
    
    [Required]
    public int RecipeId { get; set; }
    
    [StringLength(20)]
    public string Version { get; set; } = "";
    
    [StringLength(20)]
    public string ChangeType { get; set; } = ""; // CREATE, UPDATE, DELETE
    
    [StringLength(500)]
    public string? ChangeDescription { get; set; }
    
    public string? PreviousContent { get; set; } // 之前的内容
    
    public string? NewContent { get; set; } // 新的内容
    
    [StringLength(50)]
    public string? ChangedBy { get; set; }
    
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;
    
    // 外键
    [ForeignKey("RecipeId")]
    public virtual Recipe Recipe { get; set; } = null!;
}

/// <summary>
/// 系统配置实体
/// </summary>
[Table("SystemConfigs")]
public class SystemConfig
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }
    
    [Required]
    [StringLength(100)]
    public string Key { get; set; } = "";
    
    public string? Value { get; set; }
    
    [StringLength(200)]
    public string? Description { get; set; }
    
    [StringLength(50)]
    public string? UpdatedBy { get; set; }
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}