using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Options;
using IndustryMonitor.Shared.Models.Configs;
using System.Text.Json;

namespace IndustryMonitor.Server.Services.Cache;

/// <summary>
/// Redis 缓存服务实现
/// </summary>
public class RedisCacheService : ICacheService
{
    private readonly IDistributedCache _distributedCache;
    private readonly RedisConfig _config;
    private readonly ILogger<RedisCacheService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public RedisCacheService(
        IDistributedCache distributedCache,
        IOptions<RedisConfig> config,
        ILogger<RedisCacheService> logger)
    {
        _distributedCache = distributedCache;
        _config = config.Value;
        _logger = logger;
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    public async Task<T?> GetAsync<T>(string key) where T : class
    {
        try
        {
            var cachedValue = await _distributedCache.GetStringAsync(GetKey(key));
            if (string.IsNullOrEmpty(cachedValue))
                return null;

            return JsonSerializer.Deserialize<T>(cachedValue, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从 Redis 获取缓存失败: Key = {Key}", key);
            return null;
        }
    }

    public async Task<T> GetAsync<T>(string key, T defaultValue) where T : class
    {
        var result = await GetAsync<T>(key);
        return result ?? defaultValue;
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class
    {
        try
        {
            if (value == null)
                return;

            var jsonValue = JsonSerializer.Serialize(value, _jsonOptions);
            var options = new DistributedCacheEntryOptions();
            
            if (expiration.HasValue)
            {
                options.SetAbsoluteExpiration(expiration.Value);
            }
            else
            {
                options.SetAbsoluteExpiration(TimeSpan.FromMinutes(_config.DefaultExpirationMinutes));
            }

            await _distributedCache.SetStringAsync(GetKey(key), jsonValue, options);
            
            _logger.LogTrace("设置 Redis 缓存: Key = {Key}, 过期时间 = {Expiration}", 
                key, expiration?.TotalMinutes ?? _config.DefaultExpirationMinutes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置 Redis 缓存失败: Key = {Key}", key);
        }
    }

    public async Task<bool> SetAsync<T>(string key, T value, TimeSpan expiration, bool onlyIfNotExists) where T : class
    {
        try
        {
            if (onlyIfNotExists && await ExistsAsync(key))
            {
                return false;
            }

            await SetAsync(key, value, expiration);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "条件设置 Redis 缓存失败: Key = {Key}", key);
            return false;
        }
    }

    public async Task<bool> RemoveAsync(string key)
    {
        try
        {
            await _distributedCache.RemoveAsync(GetKey(key));
            _logger.LogTrace("删除 Redis 缓存: Key = {Key}", key);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除 Redis 缓存失败: Key = {Key}", key);
            return false;
        }
    }

    public async Task<bool> ExistsAsync(string key)
    {
        try
        {
            var value = await _distributedCache.GetStringAsync(GetKey(key));
            return !string.IsNullOrEmpty(value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查 Redis 缓存存在性失败: Key = {Key}", key);
            return false;
        }
    }

    public async Task<long> RemoveByPatternAsync(string pattern)
    {
        try
        {
            // 注意：这是简化实现，生产环境中应该使用专用的Redis库如StackExchange.Redis
            // 这里仅提供基本功能
            _logger.LogWarning("RemoveByPatternAsync 方法需要直接的 Redis 连接才能实现模式删除");
            await Task.CompletedTask;
            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "按模式删除 Redis 缓存失败: Pattern = {Pattern}", pattern);
            return 0;
        }
    }

    public async Task<IEnumerable<string>> GetKeysAsync(string pattern)
    {
        try
        {
            // 注意：这是简化实现，生产环境中应该使用专用的Redis库
            _logger.LogWarning("GetKeysAsync 方法需要直接的 Redis 连接才能实现模式查询");
            await Task.CompletedTask;
            return Enumerable.Empty<string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "按模式获取 Redis 键失败: Pattern = {Pattern}", pattern);
            return Enumerable.Empty<string>();
        }
    }

    public async Task<bool> SetStringAsync(string key, string value, TimeSpan? expiration = null)
    {
        try
        {
            var options = new DistributedCacheEntryOptions();
            
            if (expiration.HasValue)
            {
                options.SetAbsoluteExpiration(expiration.Value);
            }
            else
            {
                options.SetAbsoluteExpiration(TimeSpan.FromMinutes(_config.DefaultExpirationMinutes));
            }

            await _distributedCache.SetStringAsync(GetKey(key), value, options);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置 Redis 字符串缓存失败: Key = {Key}", key);
            return false;
        }
    }

    public async Task<string?> GetStringAsync(string key)
    {
        try
        {
            return await _distributedCache.GetStringAsync(GetKey(key));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取 Redis 字符串缓存失败: Key = {Key}", key);
            return null;
        }
    }

    public async Task<long> IncrementAsync(string key, long value = 1)
    {
        try
        {
            // 注意：这是简化实现，生产环境中应该使用原子操作
            var currentValue = await GetStringAsync(key);
            var numericValue = long.TryParse(currentValue, out var current) ? current : 0;
            var newValue = numericValue + value;
            
            await SetStringAsync(key, newValue.ToString(), TimeSpan.FromMinutes(_config.DefaultExpirationMinutes));
            return newValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis 递增操作失败: Key = {Key}, Value = {Value}", key, value);
            return 0;
        }
    }

    public async Task<long> DecrementAsync(string key, long value = 1)
    {
        return await IncrementAsync(key, -value);
    }

    public async Task<bool> ExpireAsync(string key, TimeSpan expiration)
    {
        try
        {
            // 注意：这需要重新设置值，生产环境中应该使用专用的Redis库
            var currentValue = await GetStringAsync(key);
            if (!string.IsNullOrEmpty(currentValue))
            {
                return await SetStringAsync(key, currentValue, expiration);
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置 Redis 过期时间失败: Key = {Key}", key);
            return false;
        }
    }

    public async Task<TimeSpan?> GetTtlAsync(string key)
    {
        try
        {
            // 注意：IDistributedCache 接口不支持 TTL 查询
            // 生产环境中应该使用专用的Redis库
            _logger.LogWarning("GetTtlAsync 方法需要直接的 Redis 连接才能实现");
            await Task.CompletedTask;
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取 Redis TTL 失败: Key = {Key}", key);
            return null;
        }
    }

    public async Task FlushDatabaseAsync()
    {
        try
        {
            // 注意：IDistributedCache 接口不支持数据库清空
            // 这是危险操作，通常不建议在应用程序中实现
            _logger.LogWarning("FlushDatabaseAsync 操作需要管理员权限和直接的 Redis 连接");
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清空 Redis 数据库失败");
        }
    }

    private string GetKey(string key)
    {
        return string.IsNullOrEmpty(_config.KeyPrefix) ? key : $"{_config.KeyPrefix}:{key}";
    }
}