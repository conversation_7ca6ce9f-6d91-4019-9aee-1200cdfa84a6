{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "Kestrel": {"Endpoints": {"Http": {"Url": "http://0.0.0.0:5000"}}}, "ConnectionStrings": {"SQLite": "Data Source=/opt/industrymonitor/database/industry.db;Cache=Shared;", "Redis": "localhost:6379,password=YourRedisPassword123,database=0,connectTimeout=5000,syncTimeout=1000"}, "JwtSettings": {"Secret": "ProductionJwtSecretKeyMustBeAtLeast32CharactersLongAndSecure!"}, "SystemSettings": {"EnableSwagger": false}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "File", "Args": {"path": "/opt/industrymonitor/logs/app-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "fileSizeLimitBytes": 104857600, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}}