using System.Linq.Expressions;

namespace IndustryMonitor.Server.Data.Repositories;

/// <summary>
/// 通用仓储接口
/// </summary>
public interface IRepository<T> where T : class
{
    Task<T?> GetByIdAsync(int id);
    Task<T?> GetByIdAsync(string id);
    Task<IEnumerable<T>> GetAllAsync();
    Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);
    Task<T?> FindOneAsync(Expression<Func<T, bool>> predicate);
    Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate);
    Task<int> CountAsync();
    Task<int> CountAsync(Expression<Func<T, bool>> predicate);
    Task<T> AddAsync(T entity);
    Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities);
    Task<T> UpdateAsync(T entity);
    Task DeleteAsync(T entity);
    Task DeleteByIdAsync(int id);
    Task DeleteRangeAsync(IEnumerable<T> entities);
    Task<(IEnumerable<T> Items, int TotalCount)> GetPagedAsync(
        int page, 
        int pageSize, 
        Expression<Func<T, bool>>? predicate = null,
        Expression<Func<T, object>>? orderBy = null,
        bool ascending = true);
}

/// <summary>
/// 设备仓储接口
/// </summary>
public interface IDeviceRepository : IRepository<Device>
{
    Task<IEnumerable<Device>> GetActiveDevicesAsync();
    Task<Device?> GetByIpAddressAsync(string ipAddress);
    Task<IEnumerable<Device>> GetDevicesByLocationAsync(string location);
    Task<IEnumerable<Device>> GetDevicesByTypeAsync(string deviceType);
    Task<bool> IsIpAddressExistsAsync(string ipAddress, int? excludeId = null);
}

/// <summary>
/// 配方仓储接口
/// </summary>
public interface IRecipeRepository : IRepository<Recipe>
{
    Task<IEnumerable<Recipe>> GetUnlockedRecipesAsync();
    Task<Recipe?> GetWithHistoryAsync(int id);
    Task<bool> IsRecipeLockedAsync(int id);
    Task<Recipe?> LockRecipeAsync(int id, string lockedBy);
    Task<bool> UnlockRecipeAsync(int id, string unlockedBy);
    Task<IEnumerable<Recipe>> GetRecipesByCreatorAsync(string createdBy);
}

/// <summary>
/// 用户仓储接口
/// </summary>
public interface IUserRepository : IRepository<User>
{
    Task<User?> GetByUsernameAsync(string username);
    Task<bool> IsUsernameExistsAsync(string username, int? excludeId = null);
    Task<IEnumerable<User>> GetActiveUsersAsync();
    Task<IEnumerable<User>> GetUsersByRoleAsync(UserRole role);
}

/// <summary>
/// 写入任务仓储接口
/// </summary>
public interface IWriteTaskRepository : IRepository<WriteTask>
{
    Task<WriteTask?> GetByTaskIdAsync(string taskId);
    Task<IEnumerable<WriteTask>> GetRunningTasksAsync();
    Task<IEnumerable<WriteTask>> GetTasksByStatusAsync(WriteTaskStatus status);
    Task<IEnumerable<WriteTask>> GetTasksByUserAsync(string username);
    Task<IEnumerable<WriteTask>> GetRecentTasksAsync(int count = 50);
}

/// <summary>
/// 操作日志仓储接口
/// </summary>
public interface IOperationLogRepository : IRepository<OperationLog>
{
    Task<IEnumerable<OperationLog>> GetLogsByUserAsync(int userId);
    Task<IEnumerable<OperationLog>> GetLogsByOperationAsync(OperationType operation);
    Task<IEnumerable<OperationLog>> GetLogsByDateRangeAsync(DateTime startDate, DateTime endDate);
    Task<IEnumerable<OperationLog>> GetRecentLogsAsync(int count = 100);
}

/// <summary>
/// 设备历史数据仓储接口
/// </summary>
public interface IDeviceHistoryRepository : IRepository<DeviceHistory>
{
    Task<IEnumerable<DeviceHistory>> GetDeviceHistoryAsync(int deviceId, DateTime startTime, DateTime endTime);
    Task<DeviceHistory?> GetLatestDataAsync(int deviceId);
    Task DeleteOldDataAsync(DateTime cutoffDate);
    Task<IEnumerable<DeviceHistory>> GetAggregatedDataAsync(
        int deviceId, 
        DateTime startTime, 
        DateTime endTime, 
        TimeSpan interval);
}

/// <summary>
/// 系统配置仓储接口
/// </summary>
public interface ISystemConfigRepository : IRepository<SystemConfig>
{
    Task<SystemConfig?> GetByKeyAsync(string key);
    Task<string?> GetValueAsync(string key);
    Task<bool> SetValueAsync(string key, string value, string? updatedBy = null);
    Task<Dictionary<string, string>> GetAllConfigsAsync();
}

/// <summary>
/// 工作单元接口
/// </summary>
public interface IUnitOfWork : IDisposable
{
    IDeviceRepository Devices { get; }
    IRecipeRepository Recipes { get; }
    IUserRepository Users { get; }
    IWriteTaskRepository WriteTasks { get; }
    IOperationLogRepository OperationLogs { get; }
    IDeviceHistoryRepository DeviceHistories { get; }
    ISystemConfigRepository SystemConfigs { get; }
    
    Task<int> SaveChangesAsync();
    Task BeginTransactionAsync();
    Task CommitTransactionAsync();
    Task RollbackTransactionAsync();
}