using Prism.Ioc;
using Prism.Modularity;
using Prism.Unity;
using System.Windows;

namespace IndustryMonitor.Client
{
    public partial class App : PrismApplication
    {
        protected override Window CreateShell()
        {
            return Container.Resolve<Views.MainWindow>();
        }

        protected override void RegisterTypes(IContainerRegistry containerRegistry)
        {
            // Register services and viewmodels here
        }
        
        protected override void ConfigureModuleCatalog(IModuleCatalog moduleCatalog)
        {
            moduleCatalog.AddModule<Modules.DeviceMonitorModule>();
        }
    }
}
