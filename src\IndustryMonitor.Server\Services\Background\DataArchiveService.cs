using Microsoft.Extensions.Options;
using IndustryMonitor.Shared.Models.Configs;
using IndustryMonitor.Server.Data.Repositories;

namespace IndustryMonitor.Server.Services.Background;

/// <summary>
/// 数据归档后台服务
/// 负责清理过期的历史数据和日志
/// </summary>
public class DataArchiveService : BackgroundService
{
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly SystemConfig _config;
    private readonly ILogger<DataArchiveService> _logger;
    
    public DataArchiveService(
        IServiceScopeFactory scopeFactory,
        IOptions<SystemConfig> config,
        ILogger<DataArchiveService> logger)
    {
        _scopeFactory = scopeFactory;
        _config = config.Value;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("数据归档后台服务已启动，清理间隔: {Interval} 小时", _config.DataRetentionHours / 24);
        
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await PerformDataCleanupAsync();
                
                // 每天执行一次清理
                await Task.Delay(TimeSpan.FromHours(24), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据归档过程中发生错误");
                
                // 发生错误时等待1小时后重试
                await Task.Delay(TimeSpan.FromHours(1), stoppingToken);
            }
        }
        
        _logger.LogInformation("数据归档后台服务已停止");
    }

    private async Task PerformDataCleanupAsync()
    {
        using var scope = _scopeFactory.CreateScope();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
        
        var cutoffDate = DateTime.UtcNow.AddHours(-_config.DataRetentionHours);
        
        _logger.LogInformation("开始清理 {CutoffDate} 之前的历史数据", cutoffDate);
        
        var totalCleaned = 0;
        
        // 清理设备历史数据
        await unitOfWork.DeviceHistories.DeleteOldDataAsync(cutoffDate);
        var deviceHistoryCleaned = await unitOfWork.SaveChangesAsync();
        totalCleaned += deviceHistoryCleaned;
        
        // 清理操作日志（保留更长时间）
        var logCutoffDate = DateTime.UtcNow.AddDays(-_config.LogRetentionDays);
        var oldLogs = await unitOfWork.OperationLogs.FindAsync(l => l.CreatedAt < logCutoffDate);
        if (oldLogs.Any())
        {
            await unitOfWork.OperationLogs.DeleteRangeAsync(oldLogs);
            var logsCleaned = await unitOfWork.SaveChangesAsync();
            totalCleaned += logsCleaned;
            
            _logger.LogInformation("清理操作日志: {Count} 条", logsCleaned);
        }
        
        _logger.LogInformation("数据归档完成，总计清理: {TotalCleaned} 条记录", totalCleaned);
    }
}