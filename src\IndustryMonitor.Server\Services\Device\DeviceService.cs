using Microsoft.Extensions.Options;
using IndustryMonitor.Shared.Models.Configs;
using IndustryMonitor.Server.Data.Repositories;
using IndustryMonitor.Shared.Models.Entities;
using IndustryMonitor.Shared.Enums;
using IndustryMonitor.Shared.Models.DTOs;

namespace IndustryMonitor.Server.Services.Device;

/// <summary>
/// 设备服务接口
/// </summary>
public interface IDeviceService
{
    Task<DeviceDto?> GetDeviceAsync(int id);
    Task<IEnumerable<DeviceDto>> GetAllDevicesAsync();
    Task<IEnumerable<DeviceDto>> GetActiveDevicesAsync();
    Task<DeviceDto> CreateDeviceAsync(CreateDeviceRequest request);
    Task<DeviceDto> UpdateDeviceAsync(int id, UpdateDeviceRequest request);
    Task DeleteDeviceAsync(int id);
    Task<DeviceStatusDto> GetDeviceStatusAsync(int deviceId);
    Task<IEnumerable<DeviceHistoryDto>> GetDeviceHistoryAsync(int deviceId, DateTime startTime, DateTime endTime);
    Task<ushort[]> ReadDeviceRegistersAsync(int deviceId);
    Task WriteDeviceRegistersAsync(int deviceId, Dictionary<int, ushort> registerValues);
    Task<bool> TestDeviceConnectionAsync(string ipAddress, int port);
}

/// <summary>
/// 设备服务实现
/// </summary>
public class DeviceService : IDeviceService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IConnectionPool _connectionPool;
    private readonly ModbusConfig _config;
    private readonly ILogger<DeviceService> _logger;

    public DeviceService(
        IUnitOfWork unitOfWork,
        IConnectionPool connectionPool,
        IOptions<ModbusConfig> config,
        ILogger<DeviceService> logger)
    {
        _unitOfWork = unitOfWork;
        _connectionPool = connectionPool;
        _config = config.Value;
        _logger = logger;
    }

    public async Task<DeviceDto?> GetDeviceAsync(int id)
    {
        try
        {
            var device = await _unitOfWork.Devices.GetByIdAsync(id);
            if (device == null)
                return null;

            return new DeviceDto
            {
                Id = device.Id,
                Name = device.Name,
                IpAddress = device.IpAddress,
                RegisterStart = device.RegisterStart,
                RegisterCount = device.RegisterCount,
                DeviceType = device.DeviceType,
                Location = device.Location,
                IsActive = device.IsActive,
                Status = device.Status,
                LastHeartbeat = device.LastHeartbeat,
                CreatedAt = device.CreatedAt,
                UpdatedAt = device.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备信息失败: DeviceId = {DeviceId}", id);
            throw;
        }
    }

    public async Task<IEnumerable<DeviceDto>> GetAllDevicesAsync()
    {
        try
        {
            var devices = await _unitOfWork.Devices.GetAllAsync();
            return devices.Select(device => new DeviceDto
            {
                Id = device.Id,
                Name = device.Name,
                IpAddress = device.IpAddress,
                RegisterStart = device.RegisterStart,
                RegisterCount = device.RegisterCount,
                DeviceType = device.DeviceType,
                Location = device.Location,
                IsActive = device.IsActive,
                Status = device.Status,
                LastHeartbeat = device.LastHeartbeat,
                CreatedAt = device.CreatedAt,
                UpdatedAt = device.UpdatedAt
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有设备列表失败");
            throw;
        }
    }

    public async Task<IEnumerable<DeviceDto>> GetActiveDevicesAsync()
    {
        try
        {
            var devices = await _unitOfWork.Devices.GetActiveDevicesAsync();
            return devices.Select(device => new DeviceDto
            {
                Id = device.Id,
                Name = device.Name,
                IpAddress = device.IpAddress,
                RegisterStart = device.RegisterStart,
                RegisterCount = device.RegisterCount,
                DeviceType = device.DeviceType,
                Location = device.Location,
                IsActive = device.IsActive,
                Status = device.Status,
                LastHeartbeat = device.LastHeartbeat,
                CreatedAt = device.CreatedAt,
                UpdatedAt = device.UpdatedAt
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取活跃设备列表失败");
            throw;
        }
    }

    public async Task<DeviceDto> CreateDeviceAsync(CreateDeviceRequest request)
    {
        try
        {
            // 检查 IP 地址是否已存在
            var existingDevice = await _unitOfWork.Devices.GetByIpAddressAsync(request.IpAddress);
            if (existingDevice != null)
            {
                throw new InvalidOperationException($"IP 地址 {request.IpAddress} 已被其他设备使用");
            }

            var device = new Shared.Models.Entities.Device
            {
                Name = request.Name,
                IpAddress = request.IpAddress,
                RegisterStart = request.RegisterStart,
                RegisterCount = request.RegisterCount,
                DeviceType = request.DeviceType ?? "PLC",
                Location = request.Location ?? "",
                IsActive = true,
                Status = DeviceStatus.Unknown,
                CreatedAt = DateTime.UtcNow
            };

            var createdDevice = await _unitOfWork.Devices.AddAsync(device);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("创建设备成功: {DeviceName} ({IpAddress})", device.Name, device.IpAddress);

            return new DeviceDto
            {
                Id = createdDevice.Id,
                Name = createdDevice.Name,
                IpAddress = createdDevice.IpAddress,
                RegisterStart = createdDevice.RegisterStart,
                RegisterCount = createdDevice.RegisterCount,
                DeviceType = createdDevice.DeviceType,
                Location = createdDevice.Location,
                IsActive = createdDevice.IsActive,
                Status = createdDevice.Status,
                LastHeartbeat = createdDevice.LastHeartbeat,
                CreatedAt = createdDevice.CreatedAt,
                UpdatedAt = createdDevice.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建设备失败: {DeviceName}", request.Name);
            throw;
        }
    }

    public async Task<DeviceDto> UpdateDeviceAsync(int id, UpdateDeviceRequest request)
    {
        try
        {
            var device = await _unitOfWork.Devices.GetByIdAsync(id);
            if (device == null)
            {
                throw new InvalidOperationException($"设备不存在: Id = {id}");
            }

            // 检查 IP 地址是否被其他设备使用
            if (!string.IsNullOrEmpty(request.IpAddress) && request.IpAddress != device.IpAddress)
            {
                var existingDevice = await _unitOfWork.Devices.GetByIpAddressAsync(request.IpAddress);
                if (existingDevice != null && existingDevice.Id != id)
                {
                    throw new InvalidOperationException($"IP 地址 {request.IpAddress} 已被其他设备使用");
                }
            }

            // 更新字段
            if (!string.IsNullOrEmpty(request.Name))
                device.Name = request.Name;
            if (!string.IsNullOrEmpty(request.IpAddress))
                device.IpAddress = request.IpAddress;
            if (request.RegisterStart.HasValue)
                device.RegisterStart = request.RegisterStart.Value;
            if (request.RegisterCount.HasValue)
                device.RegisterCount = request.RegisterCount.Value;
            if (!string.IsNullOrEmpty(request.DeviceType))
                device.DeviceType = request.DeviceType;
            if (!string.IsNullOrEmpty(request.Location))
                device.Location = request.Location;
            if (request.IsActive.HasValue)
                device.IsActive = request.IsActive.Value;

            device.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Devices.UpdateAsync(device);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("更新设备成功: {DeviceName} ({IpAddress})", device.Name, device.IpAddress);

            return new DeviceDto
            {
                Id = device.Id,
                Name = device.Name,
                IpAddress = device.IpAddress,
                RegisterStart = device.RegisterStart,
                RegisterCount = device.RegisterCount,
                DeviceType = device.DeviceType,
                Location = device.Location,
                IsActive = device.IsActive,
                Status = device.Status,
                LastHeartbeat = device.LastHeartbeat,
                CreatedAt = device.CreatedAt,
                UpdatedAt = device.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新设备失败: DeviceId = {DeviceId}", id);
            throw;
        }
    }

    public async Task DeleteDeviceAsync(int id)
    {
        try
        {
            var device = await _unitOfWork.Devices.GetByIdAsync(id);
            if (device == null)
            {
                throw new InvalidOperationException($"设备不存在: Id = {id}");
            }

            await _unitOfWork.Devices.DeleteAsync(device);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("删除设备成功: {DeviceName} ({IpAddress})", device.Name, device.IpAddress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除设备失败: DeviceId = {DeviceId}", id);
            throw;
        }
    }

    public async Task<DeviceStatusDto> GetDeviceStatusAsync(int deviceId)
    {
        try
        {
            var device = await _unitOfWork.Devices.GetByIdAsync(deviceId);
            if (device == null)
            {
                throw new InvalidOperationException($"设备不存在: Id = {deviceId}");
            }

            var latestHistory = await _unitOfWork.DeviceHistories.GetLatestDataAsync(deviceId);

            return new DeviceStatusDto
            {
                DeviceId = deviceId,
                DeviceName = device.Name,
                IpAddress = device.IpAddress,
                Status = device.Status,
                IsActive = device.IsActive,
                LastHeartbeat = device.LastHeartbeat,
                LastDataUpdate = latestHistory?.Timestamp,
                RegisterData = latestHistory?.RegisterData ?? new Dictionary<string, object>()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备状态失败: DeviceId = {DeviceId}", deviceId);
            throw;
        }
    }

    public async Task<IEnumerable<DeviceHistoryDto>> GetDeviceHistoryAsync(int deviceId, DateTime startTime, DateTime endTime)
    {
        try
        {
            var histories = await _unitOfWork.DeviceHistories.GetDeviceHistoryAsync(deviceId, startTime, endTime);
            
            return histories.Select(h => new DeviceHistoryDto
            {
                Id = h.Id,
                DeviceId = h.DeviceId,
                Timestamp = h.Timestamp,
                RegisterData = h.RegisterData,
                Status = h.Status
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备历史数据失败: DeviceId = {DeviceId}", deviceId);
            throw;
        }
    }

    public async Task<ushort[]> ReadDeviceRegistersAsync(int deviceId)
    {
        IModbusConnection? connection = null;
        try
        {
            var device = await _unitOfWork.Devices.GetByIdAsync(deviceId);
            if (device == null)
            {
                throw new InvalidOperationException($"设备不存在: Id = {deviceId}");
            }

            connection = await _connectionPool.GetReadConnectionAsync(device.IpAddress);
            
            var data = await connection.ReadHoldingRegistersAsync(
                _config.SlaveId, 
                device.RegisterStart, 
                device.RegisterCount);

            _logger.LogTrace("读取设备寄存器成功: {DeviceName}, 寄存器数量: {Count}", 
                device.Name, data.Length);

            return data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "读取设备寄存器失败: DeviceId = {DeviceId}", deviceId);
            throw;
        }
        finally
        {
            connection?.Dispose();
        }
    }

    public async Task WriteDeviceRegistersAsync(int deviceId, Dictionary<int, ushort> registerValues)
    {
        IModbusConnection? connection = null;
        try
        {
            var device = await _unitOfWork.Devices.GetByIdAsync(deviceId);
            if (device == null)
            {
                throw new InvalidOperationException($"设备不存在: Id = {deviceId}");
            }

            connection = await _connectionPool.GetWriteConnectionAsync(device.IpAddress);

            // 按地址排序并分组连续地址进行批量写入
            var sortedRegisters = registerValues.OrderBy(kv => kv.Key).ToList();
            var groups = new List<(int startAddress, ushort[] values)>();
            
            var currentGroup = new List<(int address, ushort value)> { sortedRegisters[0] };
            
            for (int i = 1; i < sortedRegisters.Count; i++)
            {
                if (sortedRegisters[i].Key == currentGroup.Last().address + 1)
                {
                    // 连续地址，添加到当前组
                    currentGroup.Add((sortedRegisters[i].Key, sortedRegisters[i].Value));
                }
                else
                {
                    // 非连续地址，完成当前组
                    groups.Add((currentGroup[0].address, currentGroup.Select(x => x.value).ToArray()));
                    currentGroup = new List<(int, ushort)> { sortedRegisters[i] };
                }
            }
            
            // 添加最后一组
            if (currentGroup.Any())
            {
                groups.Add((currentGroup[0].address, currentGroup.Select(x => x.value).ToArray()));
            }

            // 执行批量写入
            foreach (var (startAddress, values) in groups)
            {
                await connection.WriteMultipleRegistersAsync(_config.SlaveId, startAddress, values);
                
                _logger.LogTrace("写入设备寄存器: {DeviceName}, 起始地址: {StartAddress}, 数量: {Count}", 
                    device.Name, startAddress, values.Length);
            }

            _logger.LogInformation("写入设备寄存器成功: {DeviceName}, 总寄存器数: {TotalCount}", 
                device.Name, registerValues.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "写入设备寄存器失败: DeviceId = {DeviceId}", deviceId);
            throw;
        }
        finally
        {
            connection?.Dispose();
        }
    }

    public async Task<bool> TestDeviceConnectionAsync(string ipAddress, int port)
    {
        IModbusConnection? connection = null;
        try
        {
            connection = await _connectionPool.GetReadConnectionAsync(ipAddress);
            
            // 尝试读取一个寄存器来测试连接
            var testResult = await connection.ReadHoldingRegistersAsync(_config.SlaveId, 40001, 1);
            
            _logger.LogInformation("设备连接测试成功: {IpAddress}:{Port}", ipAddress, port);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "设备连接测试失败: {IpAddress}:{Port}", ipAddress, port);
            return false;
        }
        finally
        {
            connection?.Dispose();
        }
    }
}