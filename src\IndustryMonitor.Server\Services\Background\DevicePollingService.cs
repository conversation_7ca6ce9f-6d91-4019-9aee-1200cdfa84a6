using Microsoft.Extensions.Options;
using IndustryMonitor.Shared.Models.Configs;
using IndustryMonitor.Server.Data.Repositories;
using IndustryMonitor.Server.Services.Device;
using IndustryMonitor.Server.Services.Cache;
using IndustryMonitor.Server.Hubs;
using IndustryMonitor.Shared.Models.Entities;
using IndustryMonitor.Shared.Enums;
using Microsoft.AspNetCore.SignalR;
using System.Collections.Concurrent;
using System.Text.Json;

namespace IndustryMonitor.Server.Services.Background;

/// <summary>
/// 设备轮询后台服务
/// 负责按500ms周期轮询所有活跃设备并更新状态
/// </summary>
public class DevicePollingService : BackgroundService
{
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly IConnectionPool _connectionPool;
    private readonly ICacheService _cacheService;
    private readonly IHubContext<DeviceMonitorHub> _hubContext;
    private readonly ModbusConfig _config;
    private readonly ILogger<DevicePollingService> _logger;
    
    private readonly ConcurrentDictionary<int, DateTime> _lastSuccessfulReads = new();
    private readonly ConcurrentDictionary<int, DeviceStatus> _deviceStatuses = new();
    private readonly SemaphoreSlim _pollingSemaphore;
    
    // 性能计数器
    private long _totalPollingCycles = 0;
    private long _successfulReads = 0;
    private long _failedReads = 0;
    private DateTime _serviceStartTime;
    
    public DevicePollingService(
        IServiceScopeFactory scopeFactory,
        IConnectionPool connectionPool,
        ICacheService cacheService,
        IHubContext<DeviceMonitorHub> hubContext,
        IOptions<ModbusConfig> config,
        ILogger<DevicePollingService> logger)
    {
        _scopeFactory = scopeFactory;
        _connectionPool = connectionPool;
        _cacheService = cacheService;
        _hubContext = hubContext;
        _config = config.Value;
        _logger = logger;
        
        // 控制并发轮询的设备数量
        _pollingSemaphore = new SemaphoreSlim(_config.MaxConcurrentPolling, _config.MaxConcurrentPolling);
        _serviceStartTime = DateTime.UtcNow;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("设备轮询后台服务已启动，轮询间隔: {PollingInterval}ms", _config.PollingInterval);
        
        // 等待一段时间让其他服务完全启动
        await Task.Delay(5000, stoppingToken);
        
        while (!stoppingToken.IsCancellationRequested)
        {
            var cycleStartTime = DateTime.UtcNow;
            
            try
            {
                await PerformPollingCycleAsync(stoppingToken);
                _totalPollingCycles++;
                
                // 每100个周期输出一次统计信息
                if (_totalPollingCycles % 100 == 0)
                {
                    await LogPollingStatisticsAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设备轮询周期执行失败");
            }
            
            // 计算下次轮询时间，确保固定间隔
            var cycleTime = (DateTime.UtcNow - cycleStartTime).TotalMilliseconds;
            var delayTime = Math.Max(0, _config.PollingInterval - (int)cycleTime);
            
            if (delayTime > 0)
            {
                await Task.Delay(delayTime, stoppingToken);
            }
            else if (cycleTime > _config.PollingInterval * 1.5)
            {
                _logger.LogWarning("轮询周期耗时过长: {CycleTime:F2}ms，超过配置间隔 {PollingInterval}ms", 
                    cycleTime, _config.PollingInterval);
            }
        }
        
        _logger.LogInformation("设备轮询后台服务已停止");
    }

    private async Task PerformPollingCycleAsync(CancellationToken cancellationToken)
    {
        using var scope = _scopeFactory.CreateScope();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
        
        // 获取所有活跃设备
        var activeDevices = await unitOfWork.Devices.GetActiveDevicesAsync();
        
        if (!activeDevices.Any())
        {
            await Task.Delay(1000, cancellationToken); // 如果没有设备，稍作等待
            return;
        }
        
        // 并行轮询设备
        var pollingTasks = activeDevices.Select(device => PollDeviceAsync(device, cancellationToken));
        await Task.WhenAll(pollingTasks);
        
        // 批量保存历史数据
        await SaveDeviceHistoryBatchAsync(unitOfWork);
    }

    private async Task PollDeviceAsync(IndustryMonitor.Shared.Models.Entities.Device device, CancellationToken cancellationToken)
    {
        await _pollingSemaphore.WaitAsync(cancellationToken);
        
        try
        {
            var startTime = DateTime.UtcNow;
            IModbusConnection? connection = null;
            
            try
            {
                // 获取连接
                connection = await _connectionPool.GetReadConnectionAsync(device.IpAddress);
                
                // 读取设备数据
                var registerData = await connection.ReadHoldingRegistersAsync(
                    _config.SlaveId, 
                    device.RegisterStart, 
                    device.RegisterCount);
                
                // 处理读取成功
                await HandleSuccessfulReadAsync(device, registerData, startTime);
                
                Interlocked.Increment(ref _successfulReads);
            }
            catch (Exception ex)
            {
                // 处理读取失败
                await HandleFailedReadAsync(device, ex, startTime);
                
                Interlocked.Increment(ref _failedReads);
            }
            finally
            {
                connection?.Dispose();
            }
        }
        finally
        {
            _pollingSemaphore.Release();
        }
    }

    private async Task HandleSuccessfulReadAsync(
        IndustryMonitor.Shared.Models.Entities.Device device, 
        ushort[] registerData, 
        DateTime timestamp)
    {
        try
        {
            // 更新设备状态
            var previousStatus = _deviceStatuses.GetValueOrDefault(device.Id, DeviceStatus.Unknown);
            _deviceStatuses[device.Id] = DeviceStatus.Online;
            _lastSuccessfulReads[device.Id] = timestamp;
            
            // 构建寄存器数据字典
            var registerDict = new Dictionary<string, object>();
            for (int i = 0; i < registerData.Length; i++)
            {
                var address = device.RegisterStart + i;
                registerDict[$"REG_{address}"] = registerData[i];
            }
            
            // 缓存最新数据
            var cacheKey = $"device_data_{device.Id}";
            var deviceData = new
            {
                DeviceId = device.Id,
                DeviceName = device.Name,
                IpAddress = device.IpAddress,
                Status = DeviceStatus.Online,
                Timestamp = timestamp,
                RegisterData = registerDict,
                ResponseTime = (DateTime.UtcNow - timestamp).TotalMilliseconds
            };
            
            await _cacheService.SetAsync(cacheKey, deviceData, TimeSpan.FromMinutes(5));
            
            // 如果状态发生变化，发送实时通知
            if (previousStatus != DeviceStatus.Online)
            {
                await NotifyDeviceStatusChangeAsync(device, DeviceStatus.Online, timestamp);
                
                using var scope = _scopeFactory.CreateScope();
                var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
                
                // 更新数据库中的设备状态
                var dbDevice = await unitOfWork.Devices.GetByIdAsync(device.Id);
                if (dbDevice != null)
                {
                    dbDevice.Status = DeviceStatus.Online;
                    dbDevice.LastHeartbeat = timestamp;
                    await unitOfWork.Devices.UpdateAsync(dbDevice);
                    await unitOfWork.SaveChangesAsync();
                }
            }
            
            // 发送实时数据更新
            await _hubContext.Clients.Group($"Device_{device.Id}")
                .SendAsync("DeviceDataUpdate", deviceData);
            
            _logger.LogTrace("设备读取成功: {DeviceName} ({IpAddress}), 寄存器数量: {RegisterCount}", 
                device.Name, device.IpAddress, registerData.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理设备读取成功数据时出错: {DeviceName}", device.Name);
        }
    }

    private async Task HandleFailedReadAsync(
        IndustryMonitor.Shared.Models.Entities.Device device, 
        Exception exception, 
        DateTime timestamp)
    {
        try
        {
            var previousStatus = _deviceStatuses.GetValueOrDefault(device.Id, DeviceStatus.Unknown);
            var lastSuccessTime = _lastSuccessfulReads.GetValueOrDefault(device.Id, DateTime.MinValue);
            
            // 判断设备状态
            var newStatus = DeviceStatus.Error;
            var timeSinceLastSuccess = timestamp - lastSuccessTime;
            
            if (timeSinceLastSuccess.TotalMinutes > _config.DeviceOfflineTimeout)
            {
                newStatus = DeviceStatus.Offline;
            }
            
            _deviceStatuses[device.Id] = newStatus;
            
            // 缓存错误状态
            var cacheKey = $"device_data_{device.Id}";
            var deviceData = new
            {
                DeviceId = device.Id,
                DeviceName = device.Name,
                IpAddress = device.IpAddress,
                Status = newStatus,
                Timestamp = timestamp,
                ErrorMessage = exception.Message,
                LastSuccessTime = lastSuccessTime
            };
            
            await _cacheService.SetAsync(cacheKey, deviceData, TimeSpan.FromMinutes(5));
            
            // 如果状态发生变化，发送通知并更新数据库
            if (previousStatus != newStatus)
            {
                await NotifyDeviceStatusChangeAsync(device, newStatus, timestamp);
                
                using var scope = _scopeFactory.CreateScope();
                var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
                
                var dbDevice = await unitOfWork.Devices.GetByIdAsync(device.Id);
                if (dbDevice != null)
                {
                    dbDevice.Status = newStatus;
                    await unitOfWork.Devices.UpdateAsync(dbDevice);
                    await unitOfWork.SaveChangesAsync();
                }
            }
            
            // 发送错误状态更新
            await _hubContext.Clients.Group($"Device_{device.Id}")
                .SendAsync("DeviceError", deviceData);
            
            _logger.LogWarning("设备读取失败: {DeviceName} ({IpAddress}), 错误: {Error}", 
                device.Name, device.IpAddress, exception.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理设备读取失败时出错: {DeviceName}", device.Name);
        }
    }

    private async Task NotifyDeviceStatusChangeAsync(
        IndustryMonitor.Shared.Models.Entities.Device device, 
        DeviceStatus newStatus, 
        DateTime timestamp)
    {
        try
        {
            var notification = new
            {
                Type = "DeviceStatusChange",
                DeviceId = device.Id,
                DeviceName = device.Name,
                IpAddress = device.IpAddress,
                OldStatus = _deviceStatuses.GetValueOrDefault(device.Id, DeviceStatus.Unknown).ToString(),
                NewStatus = newStatus.ToString(),
                Timestamp = timestamp,
                Message = $"设备 {device.Name} 状态变更为 {GetStatusDescription(newStatus)}"
            };
            
            // 发送给所有客户端
            await _hubContext.Clients.All.SendAsync("DeviceStatusChanged", notification);
            
            _logger.LogInformation("设备状态变更: {DeviceName} -> {Status}", device.Name, newStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送设备状态变更通知失败: {DeviceName}", device.Name);
        }
    }

    private async Task SaveDeviceHistoryBatchAsync(IUnitOfWork unitOfWork)
    {
        try
        {
            var historyEntries = new List<DeviceHistory>();
            var currentTime = DateTime.UtcNow;
            
            // 从缓存中获取所有设备的最新数据
            foreach (var deviceId in _deviceStatuses.Keys)
            {
                var cacheKey = $"device_data_{deviceId}";
                var deviceData = await _cacheService.GetAsync<dynamic>(cacheKey);
                
                if (deviceData != null)
                {
                    var historyEntry = new DeviceHistory
                    {
                        DeviceId = deviceId,
                        Timestamp = currentTime,
                        Status = _deviceStatuses[deviceId],
                        RegisterData = ExtractRegisterData(deviceData)
                    };
                    
                    historyEntries.Add(historyEntry);
                }
            }
            
            if (historyEntries.Any())
            {
                await unitOfWork.DeviceHistories.AddRangeAsync(historyEntries);
                await unitOfWork.SaveChangesAsync();
                
                _logger.LogTrace("批量保存设备历史数据: {Count} 条记录", historyEntries.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量保存设备历史数据失败");
        }
    }

    private Dictionary<string, object> ExtractRegisterData(dynamic deviceData)
    {
        try
        {
            if (deviceData is JsonElement element && element.ValueKind == JsonValueKind.Object)
            {
                if (element.TryGetProperty("RegisterData", out var registerDataElement))
                {
                    return JsonSerializer.Deserialize<Dictionary<string, object>>(registerDataElement.GetRawText()) 
                           ?? new Dictionary<string, object>();
                }
            }
            
            return new Dictionary<string, object>();
        }
        catch
        {
            return new Dictionary<string, object>();
        }
    }

    private async Task LogPollingStatisticsAsync()
    {
        try
        {
            var uptime = DateTime.UtcNow - _serviceStartTime;
            var successRate = _totalPollingCycles > 0 ? 
                (_successfulReads * 100.0) / (_successfulReads + _failedReads) : 0;
            
            var poolStats = await _connectionPool.GetStatisticsAsync();
            
            _logger.LogInformation(
                "轮询统计 - 运行时长: {Uptime}, 总周期: {TotalCycles}, 成功: {Successful}, 失败: {Failed}, " +
                "成功率: {SuccessRate:F2}%, 连接池: {PoolStats}",
                uptime.ToString(@"hh\:mm\:ss"),
                _totalPollingCycles,
                _successfulReads,
                _failedReads,
                successRate,
                $"总计:{poolStats.TotalConnections} 活跃:{poolStats.ActiveConnections} 空闲:{poolStats.IdleConnections}"
            );
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "输出轮询统计信息失败");
        }
    }

    private static string GetStatusDescription(DeviceStatus status)
    {
        return status switch
        {
            DeviceStatus.Online => "在线",
            DeviceStatus.Offline => "离线",
            DeviceStatus.Error => "错误",
            DeviceStatus.Maintenance => "维护中",
            _ => "未知"
        };
    }

    public override void Dispose()
    {
        _pollingSemaphore?.Dispose();
        base.Dispose();
    }
}