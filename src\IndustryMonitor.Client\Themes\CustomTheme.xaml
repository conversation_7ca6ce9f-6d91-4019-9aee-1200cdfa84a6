<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- 主色调 - 深蓝色 -->
    <SolidColorBrush x:Key="PrimaryHueDarkBrush" Color="#1976D2"/>
    <SolidColorBrush x:Key="PrimaryHueDarkForegroundBrush" Color="#FFFFFF"/>
    
    <!-- 主色调 - 中蓝色 -->
    <SolidColorBrush x:Key="PrimaryHueMidBrush" Color="#2196F3"/>
    <SolidColorBrush x:Key="PrimaryHueMidForegroundBrush" Color="#FFFFFF"/>
    
    <!-- 主色调 - 浅蓝色 -->
    <SolidColorBrush x:Key="PrimaryHueLightBrush" Color="#BBDEFB"/>
    <SolidColorBrush x:Key="PrimaryHueLightForegroundBrush" Color="#1E1E1E"/>
    
    <!-- 强调色 - 橙色 -->
    <SolidColorBrush x:Key="SecondaryHueMidBrush" Color="#FF9800"/>
    <SolidColorBrush x:Key="SecondaryHueMidForegroundBrush" Color="#FFFFFF"/>
    
    <!-- 成功色 - 绿色 -->
    <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
    <SolidColorBrush x:Key="SuccessForegroundBrush" Color="#FFFFFF"/>
    
    <!-- 警告色 - 黄色 -->
    <SolidColorBrush x:Key="WarningBrush" Color="#FFEB3B"/>
    <SolidColorBrush x:Key="WarningForegroundBrush" Color="#1E1E1E"/>
    
    <!-- 危险色 - 红色 -->
    <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
    <SolidColorBrush x:Key="DangerForegroundBrush" Color="#FFFFFF"/>
    
    <!-- 背景色 -->
    <SolidColorBrush x:Key="BackgroundBrush" Color="#F5F5F5"/>
    
    <!-- 卡片背景色 -->
    <SolidColorBrush x:Key="CardBackgroundBrush" Color="#FFFFFF"/>
    
    <!-- 文本主色 -->
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="#212121"/>
    
    <!-- 文本次色 -->
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="#757575"/>
    
    <!-- 边框色 -->
    <SolidColorBrush x:Key="BorderBrush" Color="#E0E0E0"/>
    
    <!-- 圆角大小 -->
    <CornerRadius x:Key="DefaultCornerRadius">8</CornerRadius>
    <CornerRadius x:Key="CardCornerRadius">12</CornerRadius>
    
    <!-- 阴影效果 -->
    <DropShadowEffect x:Key="CardShadowEffect" 
                      ShadowDepth="2" 
                      BlurRadius="8" 
                      Opacity="0.15" 
                      Direction="270"/>
    
    <!-- 字体 -->
    <FontFamily x:Key="DefaultFontFamily">Roboto, Microsoft YaHei, Segoe UI, Arial</FontFamily>
    <FontFamily x:Key="HeaderFontFamily">Roboto, Microsoft YaHei, Segoe UI, Arial</FontFamily>
    
    <!-- 字体大小 -->
    <system:Double xmlns:system="clr-namespace:System;assembly=mscorlib" x:Key="FontSizeSmall">12</system:Double>
    <system:Double xmlns:system="clr-namespace:System;assembly=mscorlib" x:Key="FontSizeNormal">14</system:Double>
    <system:Double xmlns:system="clr-namespace:System;assembly=mscorlib" x:Key="FontSizeLarge">16</system:Double>
    <system:Double xmlns:system="clr-namespace:System;assembly=mscorlib" x:Key="FontSizeHeader">20</system:Double>
    <system:Double xmlns:system="clr-namespace:System;assembly=mscorlib" x:Key="FontSizeTitle">24</system:Double>
    
    <!-- 字体粗细 -->
    <FontWeight x:Key="FontWeightNormal">Normal</FontWeight>
    <FontWeight x:Key="FontWeightBold">Bold</FontWeight>
    
</ResourceDictionary>