using IndustryMonitor.Server.Data.Repositories;
using IndustryMonitor.Shared.Models.DTOs;
using IndustryMonitor.Shared.Models.Entities;
using IndustryMonitor.Shared.Enums;
using System.Text.Json;

namespace IndustryMonitor.Server.Services.Recipe;

/// <summary>
/// 配方服务接口
/// </summary>
public interface IRecipeService
{
    Task<RecipeDto?> GetRecipeAsync(int id);
    Task<IEnumerable<RecipeDto>> GetAllRecipesAsync();
    Task<IEnumerable<RecipeDto>> GetUnlockedRecipesAsync();
    Task<RecipeDto> CreateRecipeAsync(CreateRecipeRequest request);
    Task<RecipeDto> UpdateRecipeAsync(int id, UpdateRecipeRequest request);
    Task DeleteRecipeAsync(int id);
    Task<bool> LockRecipeAsync(int id, string lockedBy);
    Task<bool> UnlockRecipeAsync(int id, string unlockedBy);
    Task<RecipeDto?> CloneRecipeAsync(int sourceId, string newName, string createdBy);
    Task<bool> ValidateRecipeContentAsync(string content);
    Task<IEnumerable<RecipeHistoryDto>> GetRecipeHistoryAsync(int recipeId);
}

/// <summary>
/// 配方服务实现
/// </summary>
public class RecipeService : IRecipeService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<RecipeService> _logger;

    public RecipeService(IUnitOfWork unitOfWork, ILogger<RecipeService> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<RecipeDto?> GetRecipeAsync(int id)
    {
        try
        {
            var recipe = await _unitOfWork.Recipes.GetByIdAsync(id);
            if (recipe == null)
                return null;

            return MapToDto(recipe);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配方失败: RecipeId = {RecipeId}", id);
            throw;
        }
    }

    public async Task<IEnumerable<RecipeDto>> GetAllRecipesAsync()
    {
        try
        {
            var recipes = await _unitOfWork.Recipes.GetAllAsync();
            return recipes.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有配方失败");
            throw;
        }
    }

    public async Task<IEnumerable<RecipeDto>> GetUnlockedRecipesAsync()
    {
        try
        {
            var recipes = await _unitOfWork.Recipes.GetUnlockedRecipesAsync();
            return recipes.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取未锁定配方失败");
            throw;
        }
    }

    public async Task<RecipeDto> CreateRecipeAsync(CreateRecipeRequest request)
    {
        try
        {
            // 验证配方内容
            if (!await ValidateRecipeContentAsync(request.Content))
            {
                throw new InvalidOperationException("配方内容格式不正确");
            }

            var recipe = new IndustryMonitor.Shared.Models.Entities.Recipe
            {
                Name = request.Name,
                Description = request.Description ?? "",
                Content = request.Content,
                Version = request.Version ?? "1.0",
                CreatedBy = request.CreatedBy,
                CreatedAt = DateTime.UtcNow,
                IsLocked = false
            };

            var createdRecipe = await _unitOfWork.Recipes.AddAsync(recipe);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("创建配方成功: {RecipeName} by {CreatedBy}", recipe.Name, recipe.CreatedBy);

            return MapToDto(createdRecipe);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建配方失败: {RecipeName}", request.Name);
            throw;
        }
    }

    public async Task<RecipeDto> UpdateRecipeAsync(int id, UpdateRecipeRequest request)
    {
        try
        {
            var recipe = await _unitOfWork.Recipes.GetByIdAsync(id);
            if (recipe == null)
            {
                throw new InvalidOperationException($"配方不存在: Id = {id}");
            }

            if (recipe.IsLocked)
            {
                throw new InvalidOperationException($"配方已被锁定，无法修改: {recipe.LockedBy}");
            }

            // 创建历史记录
            await CreateRecipeHistoryAsync(recipe, "更新配方", request.UpdatedBy ?? "System");

            // 更新字段
            if (!string.IsNullOrEmpty(request.Name))
                recipe.Name = request.Name;
            if (!string.IsNullOrEmpty(request.Description))
                recipe.Description = request.Description;
            if (!string.IsNullOrEmpty(request.Content))
            {
                if (!await ValidateRecipeContentAsync(request.Content))
                {
                    throw new InvalidOperationException("配方内容格式不正确");
                }
                recipe.Content = request.Content;
            }
            if (!string.IsNullOrEmpty(request.Version))
                recipe.Version = request.Version;

            recipe.UpdatedBy = request.UpdatedBy;
            recipe.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Recipes.UpdateAsync(recipe);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("更新配方成功: {RecipeName} by {UpdatedBy}", recipe.Name, request.UpdatedBy);

            return MapToDto(recipe);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新配方失败: RecipeId = {RecipeId}", id);
            throw;
        }
    }

    public async Task DeleteRecipeAsync(int id)
    {
        try
        {
            var recipe = await _unitOfWork.Recipes.GetByIdAsync(id);
            if (recipe == null)
            {
                throw new InvalidOperationException($"配方不存在: Id = {id}");
            }

            if (recipe.IsLocked)
            {
                throw new InvalidOperationException($"配方已被锁定，无法删除: {recipe.LockedBy}");
            }

            // 检查是否有正在运行的写入任务
            var runningTasks = await _unitOfWork.WriteTasks.GetRunningTasksAsync();
            var hasRunningTasks = runningTasks.Any(t => t.RecipeId == id);
            
            if (hasRunningTasks)
            {
                throw new InvalidOperationException("配方正在被使用，无法删除");
            }

            await _unitOfWork.Recipes.DeleteAsync(recipe);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("删除配方成功: {RecipeName}", recipe.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除配方失败: RecipeId = {RecipeId}", id);
            throw;
        }
    }

    public async Task<bool> LockRecipeAsync(int id, string lockedBy)
    {
        try
        {
            var recipe = await _unitOfWork.Recipes.LockRecipeAsync(id, lockedBy);
            if (recipe != null)
            {
                await _unitOfWork.SaveChangesAsync();
                _logger.LogInformation("锁定配方成功: {RecipeName} by {LockedBy}", recipe.Name, lockedBy);
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "锁定配方失败: RecipeId = {RecipeId}", id);
            throw;
        }
    }

    public async Task<bool> UnlockRecipeAsync(int id, string unlockedBy)
    {
        try
        {
            var success = await _unitOfWork.Recipes.UnlockRecipeAsync(id, unlockedBy);
            if (success)
            {
                await _unitOfWork.SaveChangesAsync();
                _logger.LogInformation("解锁配方成功: RecipeId = {RecipeId} by {UnlockedBy}", id, unlockedBy);
            }
            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解锁配方失败: RecipeId = {RecipeId}", id);
            throw;
        }
    }

    public async Task<RecipeDto?> CloneRecipeAsync(int sourceId, string newName, string createdBy)
    {
        try
        {
            var sourceRecipe = await _unitOfWork.Recipes.GetByIdAsync(sourceId);
            if (sourceRecipe == null)
            {
                throw new InvalidOperationException($"源配方不存在: Id = {sourceId}");
            }

            var clonedRecipe = new IndustryMonitor.Shared.Models.Entities.Recipe
            {
                Name = newName,
                Description = $"基于 '{sourceRecipe.Name}' 克隆",
                Content = sourceRecipe.Content,
                Version = "1.0",
                CreatedBy = createdBy,
                CreatedAt = DateTime.UtcNow,
                IsLocked = false
            };

            var createdRecipe = await _unitOfWork.Recipes.AddAsync(clonedRecipe);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("克隆配方成功: {NewName} 基于 {SourceName} by {CreatedBy}", 
                newName, sourceRecipe.Name, createdBy);

            return MapToDto(createdRecipe);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "克隆配方失败: SourceId = {SourceId}", sourceId);
            throw;
        }
    }

    public async Task<bool> ValidateRecipeContentAsync(string content)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(content))
                return false;

            // 尝试解析 JSON
            var jsonDocument = JsonDocument.Parse(content);
            
            // 检查必要的字段结构
            if (!jsonDocument.RootElement.TryGetProperty("metadata", out _))
                return false;
                
            if (!jsonDocument.RootElement.TryGetProperty("registers", out var registersElement))
                return false;
                
            if (registersElement.ValueKind != JsonValueKind.Array)
                return false;

            // 验证寄存器数组
            foreach (var register in registersElement.EnumerateArray())
            {
                if (!register.TryGetProperty("address", out _) ||
                    !register.TryGetProperty("value", out _))
                    return false;
            }

            _logger.LogTrace("配方内容验证通过");
            return true;
        }
        catch (JsonException ex)
        {
            _logger.LogWarning("配方内容JSON格式不正确: {Error}", ex.Message);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证配方内容时发生错误");
            return false;
        }
    }

    public async Task<IEnumerable<RecipeHistoryDto>> GetRecipeHistoryAsync(int recipeId)
    {
        try
        {
            var recipe = await _unitOfWork.Recipes.GetWithHistoryAsync(recipeId);
            if (recipe?.RecipeHistories == null)
                return Enumerable.Empty<RecipeHistoryDto>();

            return recipe.RecipeHistories.Select(history => new RecipeHistoryDto
            {
                Id = history.Id,
                RecipeId = history.RecipeId,
                Action = history.Action,
                Changes = history.Changes,
                CreatedBy = history.CreatedBy,
                CreatedAt = history.CreatedAt
            }).OrderByDescending(h => h.CreatedAt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配方历史失败: RecipeId = {RecipeId}", recipeId);
            throw;
        }
    }

    private async Task CreateRecipeHistoryAsync(IndustryMonitor.Shared.Models.Entities.Recipe recipe, string action, string createdBy)
    {
        try
        {
            var history = new RecipeHistory
            {
                RecipeId = recipe.Id,
                Action = action,
                Changes = JsonSerializer.Serialize(new
                {
                    Name = recipe.Name,
                    Version = recipe.Version,
                    Content = recipe.Content,
                    ModifiedAt = DateTime.UtcNow
                }),
                CreatedBy = createdBy,
                CreatedAt = DateTime.UtcNow
            };

            await _unitOfWork.OperationLogs.AddAsync(new OperationLog
            {
                UserId = 0, // 实际应该从用户服务获取
                Operation = OperationType.RecipeManagement,
                Details = $"{action}: {recipe.Name}",
                IpAddress = "System",
                UserAgent = "RecipeService",
                CreatedAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "创建配方历史记录失败");
        }
    }

    private static RecipeDto MapToDto(IndustryMonitor.Shared.Models.Entities.Recipe recipe)
    {
        return new RecipeDto
        {
            Id = recipe.Id,
            Name = recipe.Name,
            Description = recipe.Description,
            Content = recipe.Content,
            Version = recipe.Version,
            IsLocked = recipe.IsLocked,
            LockedBy = recipe.LockedBy,
            LockedAt = recipe.LockedAt,
            CreatedBy = recipe.CreatedBy,
            CreatedAt = recipe.CreatedAt,
            UpdatedBy = recipe.UpdatedBy,
            UpdatedAt = recipe.UpdatedAt
        };
    }
}