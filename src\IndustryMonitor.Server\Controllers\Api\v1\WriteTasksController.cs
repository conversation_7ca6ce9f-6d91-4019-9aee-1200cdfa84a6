using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using IndustryMonitor.Server.Services.Recipe;
using IndustryMonitor.Shared.Models.DTOs;
using IndustryMonitor.Shared.Enums;
using System.ComponentModel.DataAnnotations;

namespace IndustryMonitor.Server.Controllers.Api.v1;

/// <summary>
/// 配方下发任务 API 控制器
/// </summary>
[ApiController]
[Route("api/v1/write-tasks")]
[Authorize]
public class WriteTasksController : ControllerBase
{
    private readonly IRecipeWriteService _writeService;
    private readonly ILogger<WriteTasksController> _logger;

    public WriteTasksController(IRecipeWriteService writeService, ILogger<WriteTasksController> logger)
    {
        _writeService = writeService;
        _logger = logger;
    }

    /// <summary>
    /// 创建配方下发任务
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,Operator")]
    public async Task<ActionResult<ApiResponse<WriteTaskDto>>> CreateWriteTask([FromBody] CreateWriteTaskRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<object>.Error("输入数据无效", GetModelStateErrors()));
            }

            // 设置创建者
            request.CreatedBy = User.Identity?.Name ?? "Anonymous";

            var task = await _writeService.CreateWriteTaskAsync(request);
            
            // 异步执行任务
            _ = Task.Run(async () =>
            {
                try
                {
                    await _writeService.ExecuteWriteTaskAsync(task.TaskId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "执行配方下发任务失败: TaskId = {TaskId}", task.TaskId);
                }
            });

            return CreatedAtAction(nameof(GetWriteTask), new { taskId = task.TaskId }, 
                ApiResponse<WriteTaskDto>.Success(task, "创建配方下发任务成功"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "创建配方下发任务业务逻辑错误");
            return BadRequest(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建配方下发任务失败");
            return StatusCode(500, ApiResponse<object>.Error("创建配方下发任务失败"));
        }
    }

    /// <summary>
    /// 获取指定的配方下发任务
    /// </summary>
    [HttpGet("{taskId}")]
    public async Task<ActionResult<ApiResponse<WriteTaskDto>>> GetWriteTask(string taskId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(taskId))
            {
                return BadRequest(ApiResponse<object>.Error("任务ID不能为空"));
            }

            var task = await _writeService.GetWriteTaskAsync(taskId);
            if (task == null)
            {
                return NotFound(ApiResponse<object>.Error("配方下发任务不存在"));
            }

            return Ok(ApiResponse<WriteTaskDto>.Success(task, "获取配方下发任务成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配方下发任务失败: TaskId = {TaskId}", taskId);
            return StatusCode(500, ApiResponse<object>.Error("获取配方下发任务失败"));
        }
    }

    /// <summary>
    /// 根据状态获取配方下发任务
    /// </summary>
    [HttpGet("by-status/{status}")]
    public async Task<ActionResult<ApiResponse<IEnumerable<WriteTaskDto>>>> GetWriteTasksByStatus(WriteTaskStatus status)
    {
        try
        {
            var tasks = await _writeService.GetWriteTasksByStatusAsync(status);
            return Ok(ApiResponse<IEnumerable<WriteTaskDto>>.Success(tasks, $"获取 {status} 状态的配方下发任务成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据状态获取配方下发任务失败: Status = {Status}", status);
            return StatusCode(500, ApiResponse<object>.Error("获取配方下发任务失败"));
        }
    }

    /// <summary>
    /// 获取最近的配方下发任务
    /// </summary>
    [HttpGet("recent")]
    public async Task<ActionResult<ApiResponse<IEnumerable<WriteTaskDto>>>> GetRecentWriteTasks([FromQuery] int count = 50)
    {
        try
        {
            if (count <= 0 || count > 200)
            {
                return BadRequest(ApiResponse<object>.Error("查询数量必须在1-200之间"));
            }

            var tasks = await _writeService.GetRecentWriteTasksAsync(count);
            return Ok(ApiResponse<IEnumerable<WriteTaskDto>>.Success(tasks, "获取最近配方下发任务成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取最近配方下发任务失败");
            return StatusCode(500, ApiResponse<object>.Error("获取最近配方下发任务失败"));
        }
    }

    /// <summary>
    /// 获取配方下发任务进度
    /// </summary>
    [HttpGet("{taskId}/progress")]
    public async Task<ActionResult<ApiResponse<WriteTaskProgressDto>>> GetWriteTaskProgress(string taskId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(taskId))
            {
                return BadRequest(ApiResponse<object>.Error("任务ID不能为空"));
            }

            var progress = await _writeService.GetWriteTaskProgressAsync(taskId);
            return Ok(ApiResponse<WriteTaskProgressDto>.Success(progress, "获取配方下发任务进度成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配方下发任务进度失败: TaskId = {TaskId}", taskId);
            return StatusCode(500, ApiResponse<object>.Error("获取配方下发任务进度失败"));
        }
    }

    /// <summary>
    /// 取消配方下发任务
    /// </summary>
    [HttpPost("{taskId}/cancel")]
    [Authorize(Roles = "Admin,Operator")]
    public async Task<ActionResult<ApiResponse<bool>>> CancelWriteTask(string taskId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(taskId))
            {
                return BadRequest(ApiResponse<object>.Error("任务ID不能为空"));
            }

            var cancelledBy = User.Identity?.Name ?? "Anonymous";
            var result = await _writeService.CancelWriteTaskAsync(taskId, cancelledBy);
            
            if (!result)
            {
                return NotFound(ApiResponse<object>.Error("配方下发任务不存在或无法取消"));
            }

            return Ok(ApiResponse<bool>.Success(result, "取消配方下发任务成功"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "取消配方下发任务业务逻辑错误: TaskId = {TaskId}", taskId);
            return BadRequest(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消配方下发任务失败: TaskId = {TaskId}", taskId);
            return StatusCode(500, ApiResponse<object>.Error("取消配方下发任务失败"));
        }
    }

    /// <summary>
    /// 手动执行配方下发任务
    /// </summary>
    [HttpPost("{taskId}/execute")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<object>>> ExecuteWriteTask(string taskId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(taskId))
            {
                return BadRequest(ApiResponse<object>.Error("任务ID不能为空"));
            }

            await _writeService.ExecuteWriteTaskAsync(taskId);
            return Ok(ApiResponse<object>.Success(null, "配方下发任务已开始执行"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "执行配方下发任务业务逻辑错误: TaskId = {TaskId}", taskId);
            return BadRequest(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行配方下发任务失败: TaskId = {TaskId}", taskId);
            return StatusCode(500, ApiResponse<object>.Error("执行配方下发任务失败"));
        }
    }

    /// <summary>
    /// 批量创建配方下发任务
    /// </summary>
    [HttpPost("batch")]
    [Authorize(Roles = "Admin,Operator")]
    public async Task<ActionResult<ApiResponse<IEnumerable<WriteTaskDto>>>> CreateBatchWriteTasks(
        [FromBody] BatchWriteTaskRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<object>.Error("输入数据无效", GetModelStateErrors()));
            }

            var createdBy = User.Identity?.Name ?? "Anonymous";
            var tasks = new List<WriteTaskDto>();

            foreach (var taskRequest in request.Tasks)
            {
                taskRequest.CreatedBy = createdBy;
                var task = await _writeService.CreateWriteTaskAsync(taskRequest);
                tasks.Add(task);

                // 异步执行任务
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _writeService.ExecuteWriteTaskAsync(task.TaskId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "执行批量配方下发任务失败: TaskId = {TaskId}", task.TaskId);
                    }
                });
            }

            return Ok(ApiResponse<IEnumerable<WriteTaskDto>>.Success(tasks, 
                $"批量创建配方下发任务成功，共 {tasks.Count} 个任务"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "批量创建配方下发任务业务逻辑错误");
            return BadRequest(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量创建配方下发任务失败");
            return StatusCode(500, ApiResponse<object>.Error("批量创建配方下发任务失败"));
        }
    }

    /// <summary>
    /// 获取配方下发统计信息
    /// </summary>
    [HttpGet("statistics")]
    [Authorize(Roles = "Admin,Operator")]
    public async Task<ActionResult<ApiResponse<WriteTaskStatisticsDto>>> GetWriteTaskStatistics()
    {
        try
        {
            // 获取各状态的任务数量
            var pendingTasks = await _writeService.GetWriteTasksByStatusAsync(WriteTaskStatus.Pending);
            var runningTasks = await _writeService.GetWriteTasksByStatusAsync(WriteTaskStatus.Running);
            var completedTasks = await _writeService.GetWriteTasksByStatusAsync(WriteTaskStatus.Completed);
            var failedTasks = await _writeService.GetWriteTasksByStatusAsync(WriteTaskStatus.Failed);
            var cancelledTasks = await _writeService.GetWriteTasksByStatusAsync(WriteTaskStatus.Cancelled);

            var statistics = new WriteTaskStatisticsDto
            {
                PendingTasks = pendingTasks.Count(),
                RunningTasks = runningTasks.Count(),
                CompletedTasks = completedTasks.Count(),
                FailedTasks = failedTasks.Count(),
                CancelledTasks = cancelledTasks.Count(),
                TotalTasks = pendingTasks.Count() + runningTasks.Count() + completedTasks.Count() + failedTasks.Count() + cancelledTasks.Count(),
                GeneratedAt = DateTime.UtcNow
            };

            return Ok(ApiResponse<WriteTaskStatisticsDto>.Success(statistics, "获取配方下发统计信息成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配方下发统计信息失败");
            return StatusCode(500, ApiResponse<object>.Error("获取配方下发统计信息失败"));
        }
    }

    private Dictionary<string, string[]> GetModelStateErrors()
    {
        return ModelState
            .Where(x => x.Value?.Errors?.Count > 0)
            .ToDictionary(
                kvp => kvp.Key,
                kvp => kvp.Value?.Errors?.Select(e => e.ErrorMessage).ToArray() ?? Array.Empty<string>()
            );
    }
}

/// <summary>
/// 批量写入任务请求
/// </summary>
public class BatchWriteTaskRequest
{
    [Required(ErrorMessage = "任务列表不能为空")]
    [MinLength(1, ErrorMessage = "至少需要一个任务")]
    [MaxLength(10, ErrorMessage = "一次最多创建10个任务")]
    public List<CreateWriteTaskRequest> Tasks { get; set; } = new();
}

/// <summary>
/// 配方下发统计信息
/// </summary>
public class WriteTaskStatisticsDto
{
    public int PendingTasks { get; set; }
    public int RunningTasks { get; set; }
    public int CompletedTasks { get; set; }
    public int FailedTasks { get; set; }
    public int CancelledTasks { get; set; }
    public int TotalTasks { get; set; }
    public DateTime GeneratedAt { get; set; }
}