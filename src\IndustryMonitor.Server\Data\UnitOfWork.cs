using Microsoft.EntityFrameworkCore.Storage;

namespace IndustryMonitor.Server.Data.Repositories;

/// <summary>
/// 工作单元实现
/// </summary>
public class UnitOfWork : IUnitOfWork, IDisposable
{
    private readonly AppDbContext _context;
    private IDbContextTransaction? _transaction;
    private bool _disposed = false;

    // 仓储实例
    private IDeviceRepository? _devices;
    private IRecipeRepository? _recipes;
    private IUserRepository? _users;
    private IWriteTaskRepository? _writeTasks;
    private IOperationLogRepository? _operationLogs;
    private IDeviceHistoryRepository? _deviceHistories;
    private ISystemConfigRepository? _systemConfigs;

    public UnitOfWork(AppDbContext context)
    {
        _context = context;
    }

    public IDeviceRepository Devices => 
        _devices ??= new DeviceRepository(_context);

    public IRecipeRepository Recipes => 
        _recipes ??= new RecipeRepository(_context);

    public IUserRepository Users => 
        _users ??= new UserRepository(_context);

    public IWriteTaskRepository WriteTasks => 
        _writeTasks ??= new WriteTaskRepository(_context);

    public IOperationLogRepository OperationLogs => 
        _operationLogs ??= new OperationLogRepository(_context);

    public IDeviceHistoryRepository DeviceHistories => 
        _deviceHistories ??= new DeviceHistoryRepository(_context);

    public ISystemConfigRepository SystemConfigs => 
        _systemConfigs ??= new SystemConfigRepository(_context);

    public async Task<int> SaveChangesAsync()
    {
        return await _context.SaveChangesAsync();
    }

    public async Task BeginTransactionAsync()
    {
        if (_transaction == null)
        {
            _transaction = await _context.Database.BeginTransactionAsync();
        }
    }

    public async Task CommitTransactionAsync()
    {
        if (_transaction != null)
        {
            try
            {
                await _context.SaveChangesAsync();
                await _transaction.CommitAsync();
            }
            catch
            {
                await RollbackTransactionAsync();
                throw;
            }
            finally
            {
                _transaction.Dispose();
                _transaction = null;
            }
        }
    }

    public async Task RollbackTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.RollbackAsync();
            _transaction.Dispose();
            _transaction = null;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                _transaction?.Dispose();
                _context.Dispose();
            }
            _disposed = true;
        }
    }
}