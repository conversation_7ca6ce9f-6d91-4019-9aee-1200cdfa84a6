using IndustryMonitor.Client.Models;
using Prism.Mvvm;
using System.Collections.ObjectModel;

namespace IndustryMonitor.Client.ViewModels
{
    public class RecipeListViewModel : BindableBase
    {
        private ObservableCollection<RecipeModel> _recipes = new();
        public ObservableCollection<RecipeModel> Recipes
        {
            get => _recipes;
            set => SetProperty(ref _recipes, value);
        }

        public RecipeListViewModel()
        {
            // For demonstration, we'll add some sample data
            // In a real application, this data would come from an API service
            Recipes.Add(new RecipeModel
            {
                Id = 1,
                Name = "Recipe 1",
                Version = "1.0",
                Description = "First recipe",
                CreatedAt = DateTime.Now.AddDays(-10),
                IsLocked = false
            });

            Recipes.Add(new RecipeModel
            {
                Id = 2,
                Name = "Recipe 2",
                Version = "2.0",
                Description = "Second recipe",
                CreatedAt = DateTime.Now.AddDays(-5),
                IsLocked = true
            });
        }
    }
}