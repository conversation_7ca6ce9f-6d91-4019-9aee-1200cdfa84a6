using IndustryMonitor.Server.Data;
using IndustryMonitor.Server.Data.Repositories;
using IndustryMonitor.Server.Services.Device;
using IndustryMonitor.Server.Services.Recipe;
using IndustryMonitor.Server.Services.Cache;
using IndustryMonitor.Server.Services.Auth;
using IndustryMonitor.Server.Services.Background;
using IndustryMonitor.Server.Hubs;
using IndustryMonitor.Server.Middleware;
using IndustryMonitor.Shared.Models.Configs;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Microsoft.OpenApi.Models;
using AutoMapper;
using FluentValidation;

namespace IndustryMonitor.Server.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddIndustryMonitorServices(this IServiceCollection services, IConfiguration configuration)
    {
        // 配置绑定
        services.Configure<ModbusConfig>(configuration.GetSection("ModbusSettings"));
        services.Configure<RedisConfig>(configuration.GetSection("RedisSettings"));
        services.Configure<JwtConfig>(configuration.GetSection("JwtSettings"));
        services.Configure<SystemConfig>(configuration.GetSection("SystemSettings"));

        // 数据库配置
        services.AddDbContext<AppDbContext>(options =>
        {
            var connectionString = configuration.GetConnectionString("SQLite");
            options.UseSqlite(connectionString);
            options.EnableSensitiveDataLogging(false);
            options.EnableServiceProviderCaching(true);
        });

        // Redis 缓存配置
        var redisConnection = configuration.GetConnectionString("Redis");
        if (!string.IsNullOrEmpty(redisConnection))
        {
            services.AddStackExchangeRedisCache(options =>
            {
                options.Configuration = redisConnection;
                options.InstanceName = "IndustryMonitor";
            });
        }

        // 内存缓存
        services.AddMemoryCache();

        // AutoMapper
        services.AddAutoMapper(typeof(Program));

        // 验证器
        services.AddValidatorsFromAssembly(typeof(Program).Assembly);

        // 数据访问层
        services.AddScoped<IUnitOfWork, UnitOfWork>();
        services.AddScoped<IDeviceRepository, DeviceRepository>();
        services.AddScoped<IRecipeRepository, RecipeRepository>();
        services.AddScoped<IUserRepository, UserRepository>();
        services.AddScoped<IWriteTaskRepository, WriteTaskRepository>();
        services.AddScoped<IOperationLogRepository, OperationLogRepository>();
        services.AddScoped<IDeviceHistoryRepository, DeviceHistoryRepository>();
        services.AddScoped<ISystemConfigRepository, SystemConfigRepository>();

        // 业务服务注册
        services.AddScoped<IDeviceService, DeviceService>();
        services.AddScoped<IRecipeService, RecipeService>();
        services.AddScoped<IRecipeWriteService, RecipeWriteService>();
        services.AddScoped<IAuthService, AuthService>();

        // 缓存服务
        services.AddSingleton<ICacheService, RedisCacheService>();
        services.AddSingleton<IMemoryCacheService, MemoryCacheService>();

        // 连接池服务
        services.AddSingleton<IConnectionPool, ConnectionPoolService>();

        // 后台服务
        services.AddHostedService<DevicePollingService>();
        services.AddHostedService<DataArchiveService>();
        services.AddHostedService<HealthCheckService>();

        // SignalR
        services.AddSignalR(options =>
        {
            options.EnableDetailedErrors = true;
            options.MaximumReceiveMessageSize = 1024 * 1024; // 1MB
            options.StreamBufferCapacity = 10;
        });

        // 控制器
        services.AddControllers()
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.PropertyNamingPolicy = null; // 保持原始命名
                options.JsonSerializerOptions.WriteIndented = false;
            });

        // API 版本控制
        services.AddApiVersioning(opt =>
        {
            opt.DefaultApiVersion = new Microsoft.AspNetCore.Mvc.ApiVersion(1, 0);
            opt.AssumeDefaultVersionWhenUnspecified = true;
            opt.ApiVersionReader = Microsoft.AspNetCore.Mvc.ApiVersionReader.Combine(
                new Microsoft.AspNetCore.Mvc.QueryStringApiVersionReader("apiVersion"),
                new Microsoft.AspNetCore.Mvc.HeaderApiVersionReader("X-Version"),
                new Microsoft.AspNetCore.Mvc.UrlSegmentApiVersionReader()
            );
        });

        services.AddVersionedApiExplorer(setup =>
        {
            setup.GroupNameFormat = "'v'VVV";
            setup.SubstituteApiVersionInUrl = true;
        });

        // JWT 认证
        var jwtSettings = configuration.GetSection("JwtSettings");
        var secretKey = jwtSettings["Secret"];
        
        services.AddAuthentication(options =>
        {
            options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
        })
        .AddJwtBearer(options =>
        {
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey!)),
                ValidateIssuer = true,
                ValidIssuer = jwtSettings["Issuer"],
                ValidateAudience = true,
                ValidAudience = jwtSettings["Audience"],
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            // SignalR 支持
            options.Events = new JwtBearerEvents
            {
                OnMessageReceived = context =>
                {
                    var accessToken = context.Request.Query["access_token"];
                    var path = context.HttpContext.Request.Path;
                    
                    if (!string.IsNullOrEmpty(accessToken) && path.StartsWithSegments("/hubs"))
                    {
                        context.Token = accessToken;
                    }
                    return Task.CompletedTask;
                }
            };
        });

        // 授权
        services.AddAuthorization(options =>
        {
            options.AddPolicy("AdminOnly", policy => policy.RequireRole("Admin"));
            options.AddPolicy("OperatorOrAdmin", policy => policy.RequireRole("Operator", "Admin"));
        });

        // Swagger
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo 
            { 
                Title = "工业设备监控系统 API", 
                Version = "v1",
                Description = "工业设备监控与配方下发系统的 REST API",
                Contact = new OpenApiContact
                {
                    Name = "技术支持",
                    Email = "<EMAIL>"
                }
            });

            // JWT Bearer 认证
            c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Description = "JWT 授权头使用 Bearer scheme. 格式: \"Authorization: Bearer {token}\"",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey,
                Scheme = "Bearer"
            });

            c.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    Array.Empty<string>()
                }
            });

            // 包含 XML 注释
            var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            if (File.Exists(xmlPath))
            {
                c.IncludeXmlComments(xmlPath);
            }
        });

        // CORS
        services.AddCors(options =>
        {
            options.AddPolicy("AllowSpecificOrigins", builder =>
            {
                builder.WithOrigins("http://localhost:3000", "https://localhost:3001") // 客户端地址
                       .AllowAnyMethod()
                       .AllowAnyHeader()
                       .AllowCredentials();
            });
        });

        // 健康检查
        services.AddHealthChecks()
            .AddDbContextCheck<AppDbContext>()
            .AddRedis(redisConnection ?? "localhost:6379");

        return services;
    }
}

public static class ApplicationBuilderExtensions
{
    public static WebApplication UseIndustryMonitorMiddleware(this WebApplication app)
    {
        // 开发环境配置
        if (app.Environment.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "工业设备监控系统 API v1");
                c.RoutePrefix = "swagger";
                c.DisplayRequestDuration();
                c.EnableDeepLinking();
            });
        }
        else
        {
            app.UseExceptionHandler("/Home/Error");
            app.UseHsts();
        }

        // 全局异常处理中间件
        app.UseMiddleware<ExceptionMiddleware>();

        // 请求日志中间件
        app.UseMiddleware<LoggingMiddleware>();

        // 静态文件
        app.UseStaticFiles();

        // 路由
        app.UseRouting();

        // CORS
        app.UseCors("AllowSpecificOrigins");

        // 认证和授权
        app.UseAuthentication();
        app.UseAuthorization();

        // 控制器路由
        app.MapControllers();

        // SignalR Hub
        app.MapHub<DeviceMonitorHub>("/hubs/deviceMonitor");
        app.MapHub<NotificationHub>("/hubs/notification");

        // 健康检查
        app.MapHealthChecks("/health");

        // API 版本信息
        app.MapGet("/", () => new { 
            Application = "工业设备监控系统", 
            Version = "1.0.0",
            Environment = app.Environment.EnvironmentName,
            Timestamp = DateTime.UtcNow 
        });

        return app;
    }
}