# 工业设备监控与配方下发系统 - 完整技术文档

## 目录

1. [项目概述](#1-项目概述)
2. [系统架构设计](#2-系统架构设计)
3. [服务端详细设计](#3-服务端详细设计)
4. [客户端详细设计](#4-客户端详细设计)
5. [数据库设计](#5-数据库设计)
6. [API接口设计](#6-api接口设计)
7. [部署方案](#7-部署方案)
8. [安全设计](#8-安全设计)
9. [性能优化](#9-性能优化)
10. [测试方案](#10-测试方案)
11. [项目交付](#11-项目交付)
12. [附录](#12-附录)

---

## 1. 项目概述

### 1.1 项目背景

本项目旨在开发一套工业设备监控与配方下发系统，用于实时监控200台PLC设备的运行状态，并支持配方文件的集中管理和批量下发。系统采用C/S架构，服务端负责设备通信和数据处理，客户端提供友好的桌面操作界面。

### 1.2 系统目标

- **实时监控**: 以500ms周期稳定采集200台设备的数据
- **配方管理**: 支持配方文件的创建、编辑、版本控制和批量下发
- **高可用性**: 故障隔离、自动恢复、数据缓存
- **易维护性**: 集中部署、统一管理、操作简便

### 1.3 技术选型

| 组件 | 技术栈 | 版本 | 说明 |
|------|--------|------|------|
| 服务端框架 | ASP.NET Core | 8.0 | Web API + SignalR |
| 客户端框架 | WPF | .NET 8 | 桌面应用程序 |
| 数据库 | SQLite | 3.x | 轻量级嵌入式数据库 |
| 缓存 | Redis | 6.0+ | 内存缓存和实时数据 |
| 通信协议 | Modbus TCP | - | 工业设备通信 |
| 通信库 | HslCommunication | 12.1.2 | .NET工业通信库 |
| UI框架 | Material Design | 4.x | 现代化UI设计 |
| MVVM框架 | Prism | 8.x | MVVM架构支持 |
| 开发工具 | Visual Studio | 2022 | 集成开发环境 |

### 1.4 系统特点

#### 1.4.1 架构特点
- **C/S架构**: 服务端集中部署，客户端分布式安装
- **分层设计**: 清晰的分层架构，便于维护和扩展
- **微服务思想**: 服务解耦，职责单一
- **事件驱动**: SignalR实现实时数据推送

#### 1.4.2 性能特点
- **高并发**: 支持80个并发Modbus连接
- **高频率**: 500ms稳定数据采集周期
- **大容量**: 支持200台设备同时监控
- **低延迟**: Redis缓存实现毫秒级数据访问

#### 1.4.3 可靠性特点
- **故障隔离**: 单设备故障不影响其他设备
- **自动重连**: 网络断线自动重连机制
- **数据备份**: 多级缓存保证数据不丢失
- **日志审计**: 完整的操作日志和错误记录

---

## 2. 系统架构设计

### 2.1 整体架构图

```
┌────────────────────────────────────────────────────────────┐
│                    WPF桌面客户端 (1-4台)                     │
│  ┌────────────┐  ┌────────────┐  ┌───────────────┐        │
│  │ 设备监控    │  │ 配方管理    │  │ 系统管理      │        │
│  └────────────┘  └────────────┘  └───────────────┘        │
└──────────────────────┬─────────────────────────────────────┘
                       │ HTTP REST API / SignalR WebSocket
┌──────────────────────▼─────────────────────────────────────┐
│                  ASP.NET Core 服务端                         │
│  ┌────────────┐  ┌────────────┐  ┌───────────────┐        │
│  │ Web API    │  │ SignalR Hub│  │ 后台服务      │        │
│  │ 控制器     │  │ 实时推送   │  │ 定时任务      │        │
│  └────────────┘  └────────────┘  └───────────────┘        │
│  ┌─────────────────────────────────────────────────┐       │
│  │              业务逻辑层                           │       │
│  │  ・设备通信服务  ・配方管理服务  ・任务调度      │       │
│  │  ・连接池管理   ・缓存服务     ・日志服务       │       │
│  └─────────────────────────────────────────────────┘       │
│  ┌────────────┐  ┌────────────┐  ┌───────────────┐        │
│  │ Redis缓存   │  │ SQLite DB  │  │ 文件存储      │        │
│  │ 实时数据    │  │ 配方数据   │  │ 配方文件      │        │
│  └────────────┘  └────────────┘  └───────────────┘        │
└──────────────────────┬─────────────────────────────────────┘
                       │ Modbus TCP (Port 502)
┌──────────────────────▼─────────────────────────────────────┐
│                    200台 PLC 设备                            │
│  192.168.1.10-209  每台设备5-10个寄存器                    │
└────────────────────────────────────────────────────────────┘
```

### 2.2 数据流设计

#### 2.2.1 设备数据采集流程

```
                    ┌─────────────┐
                    │ 定时器触发   │
                    │ (500ms)    │
                    └──────┬──────┘
                           │
                    ┌──────▼──────┐
                    │ 获取设备列表 │
                    │ (活跃设备)  │
                    └──────┬──────┘
                           │
                    ┌──────▼──────┐
                    │ 并发控制     │
                    │ (80个连接)  │
                    └──────┬──────┘
                           │
              ┌────────────▼────────────┐
              │                         │
    ┌─────────▼─────────┐    ┌─────────▼─────────┐
    │ Modbus 读取设备1   │    │ Modbus 读取设备n   │
    │ 192.168.1.10     │    │ 192.168.1.xxx    │
    └─────────┬─────────┘    └─────────┬─────────┘
              │                         │
              └────────────┬────────────┘
                           │
                    ┌──────▼──────┐
                    │ 更新Redis    │
                    │ 缓存数据     │
                    └──────┬──────┘
                           │
                    ┌──────▼──────┐
                    │ SignalR推送  │
                    │ 到客户端     │
                    └──────┬──────┘
                           │
                    ┌──────▼──────┐
                    │ 异步写入     │
                    │ 历史数据库   │
                    └─────────────┘
```

#### 2.2.2 配方下发流程

```
客户端                     服务端                    PLC设备
  │                         │                         │
  │ 1.选择配方和设备         │                         │
  │───────────────────────▶ │                         │
  │                         │                         │
  │                         │ 2.锁定相关设备          │
  │                         │───────────────────────▶ │
  │                         │                         │
  │                         │ 3.暂停数据采集          │
  │                         │                         │
  │                         │ 4.分批写入配方          │
  │                         │───────────────────────▶ │
  │ 5.接收进度更新          │                         │
  │◀─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ │                         │
  │                         │                         │
  │                         │ 6.验证写入结果          │
  │                         │◀─────────────────────── │
  │                         │                         │
  │                         │ 7.恢复数据采集          │
  │                         │                         │
  │ 8.下发完成通知          │                         │
  │◀─────────────────────── │                         │
```

### 2.3 并发与性能设计

#### 2.3.1 连接池架构

```csharp
// 连接池设计
public interface IConnectionPool
{
    Task<IModbusConnection> GetReadConnectionAsync(string ipAddress);
    Task<IModbusConnection> GetWriteConnectionAsync(string ipAddress);
    void ReturnConnection(IModbusConnection connection);
}

// 连接池配置
public class ConnectionPoolConfig
{
    public int MaxReadConnections { get; set; } = 80;    // 读连接池大小
    public int MaxWriteConnections { get; set; } = 20;   // 写连接池大小
    public int ConnectionTimeout { get; set; } = 3000;   // 连接超时(ms)
    public int IdleTimeout { get; set; } = 300000;       // 空闲超时(ms)
}
```

#### 2.3.2 缓存策略设计

```
┌─────────────────────────────────┐  ← 1ms访问延迟
│      内存缓存 (L1 Cache)         │
│   ConcurrentDictionary          │
│   最新设备状态 (200台 × 50KB)    │
└─────────────────┬───────────────┘
                  │ 异步同步
┌─────────────────▼───────────────┐  ← 1-5ms访问延迟
│      Redis缓存 (L2 Cache)        │
│   Hash + SortedSet              │
│   实时数据 + 5分钟历史数据       │
└─────────────────┬───────────────┘
                  │ 定期归档
┌─────────────────▼───────────────┐  ← 100ms+访问延迟
│      SQLite数据库 (持久存储)      │
│   关系型表结构                  │
│   历史数据 + 配方管理           │
└─────────────────────────────────┘
```

#### 2.3.3 性能指标预估

| 指标 | 预期值 | 说明 |
|------|--------|------|
| 设备采集周期 | 500ms | 稳定的数据采集频率 |
| 最大并发连接 | 80个 | Modbus TCP读取连接 |
| 单次读取耗时 | 50-200ms | 依赖网络延迟 |
| 内存使用量 | < 500MB | 服务端总内存占用 |
| CPU使用率 | < 20% | 正常运行时CPU占用 |
| 网络带宽 | < 100KB/s | 总网络流量 |
| 数据响应延迟 | < 100ms | 客户端数据刷新延迟 |

---

## 3. 服务端详细设计

### 3.1 项目结构

```
IndustryMonitor.Server/
├── Program.cs                          # 应用程序入口
├── Startup.cs                          # 服务配置和中间件
├── appsettings.json                    # 基础配置文件
├── appsettings.Development.json        # 开发环境配置
├── appsettings.Production.json         # 生产环境配置
│
├── Controllers/                        # API控制器层
│   ├── DeviceController.cs            # 设备管理API
│   ├── RecipeController.cs            # 配方管理API
│   ├── AuthController.cs              # 身份认证API
│   ├── SystemController.cs            # 系统管理API
│   └── HealthController.cs            # 健康检查API
│
├── Services/                          # 业务服务层
│   ├── Device/                        # 设备相关服务
│   │   ├── IDeviceService.cs         # 设备服务接口
│   │   ├── DeviceService.cs          # 设备管理服务
│   │   ├── DeviceReadService.cs      # 设备读取服务
│   │   ├── ModbusService.cs          # Modbus通信封装
│   │   └── ConnectionPoolService.cs   # 连接池管理服务
│   ├── Recipe/                        # 配方相关服务
│   │   ├── IRecipeService.cs         # 配方服务接口
│   │   ├── RecipeService.cs          # 配方管理服务
│   │   ├── RecipeWriteService.cs     # 配方下发服务
│   │   ├── RecipeFileService.cs      # 配方文件管理
│   │   └── RecipeLockService.cs      # 配方锁定服务
│   ├── Cache/                         # 缓存相关服务
│   │   ├── ICacheService.cs          # 缓存服务接口
│   │   ├── RedisCacheService.cs      # Redis缓存实现
│   │   └── MemoryCacheService.cs     # 内存缓存实现
│   ├── Auth/                          # 认证相关服务
│   │   ├── IAuthService.cs           # 认证服务接口
│   │   ├── AuthService.cs            # JWT认证实现
│   │   └── PermissionService.cs      # 权限管理服务
│   └── Background/                    # 后台服务
│       ├── DevicePollingService.cs    # 设备轮询后台服务
│       ├── DataArchiveService.cs      # 数据归档服务
│       └── HealthCheckService.cs      # 系统健康检查
│
├── Hubs/                              # SignalR实时通信
│   ├── DeviceMonitorHub.cs           # 设备监控Hub
│   ├── NotificationHub.cs            # 通知推送Hub
│   └── AdminHub.cs                   # 管理员专用Hub
│
├── Models/                            # 数据模型层
│   ├── Entities/                      # 实体模型
│   │   ├── Device.cs                 # 设备实体
│   │   ├── Recipe.cs                 # 配方实体
│   │   ├── User.cs                   # 用户实体
│   │   ├── OperationLog.cs          # 操作日志实体
│   │   └── SystemConfig.cs          # 系统配置实体
│   ├── DTOs/                         # 数据传输对象
│   │   ├── DeviceStatusDto.cs       # 设备状态DTO
│   │   ├── RecipeDto.cs              # 配方DTO
│   │   ├── WriteTaskDto.cs           # 写入任务DTO
│   │   └── UserDto.cs                # 用户DTO
│   ├── Requests/                     # 请求模型
│   │   ├── LoginRequest.cs           # 登录请求
│   │   ├── CreateRecipeRequest.cs    # 创建配方请求
│   │   └── ExecuteRecipeRequest.cs   # 执行配方请求
│   ├── Responses/                    # 响应模型
│   │   ├── ApiResponse.cs            # 通用API响应
│   │   ├── LoginResponse.cs          # 登录响应
│   │   └── PagedResponse.cs          # 分页响应
│   └── Configs/                      # 配置模型
│       ├── ModbusConfig.cs           # Modbus配置
│       ├── RedisConfig.cs            # Redis配置
│       └── JwtConfig.cs              # JWT配置
│
├── Data/                              # 数据访问层
│   ├── AppDbContext.cs               # EF Core上下文
│   ├── Repositories/                 # 仓储模式
│   │   ├── IRepository.cs           # 通用仓储接口
│   │   ├── IDeviceRepository.cs     # 设备仓储接口
│   │   ├── DeviceRepository.cs      # 设备仓储实现
│   │   ├── IRecipeRepository.cs     # 配方仓储接口
│   │   ├── RecipeRepository.cs      # 配方仓储实现
│   │   └── UnitOfWork.cs            # 工作单元模式
│   └── Migrations/                   # 数据库迁移文件
│       └── [自动生成的迁移文件]
│
├── Middleware/                        # 中间件
│   ├── ExceptionMiddleware.cs        # 全局异常处理
│   ├── LoggingMiddleware.cs         # 请求日志记录
│   ├── AuthenticationMiddleware.cs   # 身份验证中间件
│   └── RateLimitingMiddleware.cs    # 限流中间件
│
├── Utils/                            # 工具类
│   ├── ModbusHelper.cs              # Modbus辅助方法
│   ├── JsonHelper.cs                # JSON处理工具
│   ├── SecurityHelper.cs            # 安全相关工具
│   ├── ValidationHelper.cs          # 数据验证工具
│   └── LoggerHelper.cs              # 日志辅助工具
│
└── Extensions/                       # 扩展方法
    ├── ServiceCollectionExtensions.cs # 依赖注入扩展
    ├── ApplicationBuilderExtensions.cs # 中间件扩展
    └── StringExtensions.cs            # 字符串扩展方法
```

### 3.2 核心服务实现

#### 3.2.1 设备读取服务

```csharp
/// <summary>
/// 设备读取后台服务 - 负责定时读取所有设备数据
/// </summary>
public class DevicePollingService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IConnectionPool _connectionPool;
    private readonly ICacheService _cache;
    private readonly IHubContext<DeviceMonitorHub> _hubContext;
    private readonly ILogger<DevicePollingService> _logger;
    private readonly SemaphoreSlim _semaphore;
    private readonly Timer _timer;
    private readonly ModbusConfig _config;
    
    public DevicePollingService(
        IServiceProvider serviceProvider,
        IConnectionPool connectionPool,
        ICacheService cache,
        IHubContext<DeviceMonitorHub> hubContext,
        ILogger<DevicePollingService> logger,
        IOptions<ModbusConfig> config)
    {
        _serviceProvider = serviceProvider;
        _connectionPool = connectionPool;
        _cache = cache;
        _hubContext = hubContext;
        _logger = logger;
        _config = config.Value;
        
        // 并发控制：最多80个同时连接
        _semaphore = new SemaphoreSlim(_config.MaxReadConnections, _config.MaxReadConnections);
        
        // 定时器：每500ms触发一次
        _timer = new Timer(PollAllDevices, null, TimeSpan.Zero, TimeSpan.FromMilliseconds(500));
    }
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("设备轮询服务启动");
        
        while (!stoppingToken.IsCancellationRequested)
        {
            await Task.Delay(1000, stoppingToken); // 每秒检查一次服务状态
        }
        
        _logger.LogInformation("设备轮询服务停止");
    }
    
    private async void PollAllDevices(object state)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var deviceRepo = scope.ServiceProvider.GetRequiredService<IDeviceRepository>();
            
            // 获取所有活跃设备
            var devices = await deviceRepo.GetActiveDevicesAsync();
            
            // 过滤掉正在写入的设备
            var availableDevices = new List<Device>();
            foreach (var device in devices)
            {
                var isWriting = await _cache.GetAsync<bool>($"device:{device.Id}:writing");
                if (!isWriting)
                {
                    availableDevices.Add(device);
                }
            }
            
            // 并发读取设备数据
            var tasks = availableDevices.Select(device => ReadDeviceAsync(device)).ToArray();
            await Task.WhenAll(tasks);
            
            _logger.LogTrace($"本轮读取完成，共处理 {availableDevices.Count} 台设备");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设备轮询过程中出现异常");
        }
    }
    
    private async Task ReadDeviceAsync(Device device)
    {
        await _semaphore.WaitAsync();
        
        try
        {
            var connection = await _connectionPool.GetReadConnectionAsync(device.IpAddress);
            
            // 读取设备寄存器数据
            var registers = await ReadModbusRegistersAsync(connection, device);
            
            // 构造设备状态对象
            var deviceStatus = new DeviceStatusDto
            {
                DeviceId = device.Id,
                DeviceName = device.Name,
                IpAddress = device.IpAddress,
                Status = DeviceStatus.Online,
                Registers = registers,
                LastUpdate = DateTime.UtcNow,
                Quality = DataQuality.Good
            };
            
            // 更新内存缓存
            await _cache.SetAsync($"device:{device.Id}:status", deviceStatus, TimeSpan.FromMinutes(5));
            
            // 更新设备最后在线时间
            await _cache.SetAsync($"device:{device.Id}:lastOnline", DateTime.UtcNow, TimeSpan.FromDays(1));
            
            // 通过SignalR推送到所有客户端
            await _hubContext.Clients.All.SendAsync("DeviceStatusUpdate", deviceStatus);
            
            // 异步记录历史数据（不阻塞主流程）
            _ = Task.Run(async () => await RecordHistoryDataAsync(deviceStatus));
        }
        catch (ModbusException ex)
        {
            await HandleDeviceModbusError(device, ex);
        }
        catch (TimeoutException ex)
        {
            await HandleDeviceTimeout(device, ex);
        }
        catch (Exception ex)
        {
            await HandleDeviceGeneralError(device, ex);
        }
        finally
        {
            _semaphore.Release();
        }
    }
    
    private async Task<List<RegisterData>> ReadModbusRegistersAsync(IModbusConnection connection, Device device)
    {
        var registers = new List<RegisterData>();
        
        try
        {
            // 批量读取寄存器（一次最多读取100个连续寄存器）
            var values = await connection.ReadHoldingRegistersAsync(
                device.SlaveId, 
                device.RegisterStart, 
                device.RegisterCount);
            
            for (int i = 0; i < values.Length; i++)
            {
                registers.Add(new RegisterData
                {
                    Address = device.RegisterStart + i,
                    Value = values[i],
                    Timestamp = DateTime.UtcNow
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"读取设备 {device.Name}({device.IpAddress}) 寄存器失败");
            throw;
        }
        
        return registers;
    }
    
    private async Task HandleDeviceModbusError(Device device, ModbusException ex)
    {
        _logger.LogWarning($"设备 {device.Name}({device.IpAddress}) Modbus通信异常: {ex.Message}");
        
        var errorStatus = new DeviceStatusDto
        {
            DeviceId = device.Id,
            DeviceName = device.Name,
            IpAddress = device.IpAddress,
            Status = DeviceStatus.Error,
            ErrorMessage = ex.Message,
            LastUpdate = DateTime.UtcNow,
            Quality = DataQuality.Bad
        };
        
        await _cache.SetAsync($"device:{device.Id}:status", errorStatus, TimeSpan.FromMinutes(5));
        await _hubContext.Clients.All.SendAsync("DeviceStatusUpdate", errorStatus);
    }
    
    private async Task RecordHistoryDataAsync(DeviceStatusDto deviceStatus)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            
            // 记录到历史数据表（可考虑批量插入优化）
            var historyRecord = new DeviceHistory
            {
                DeviceId = deviceStatus.DeviceId,
                Data = JsonSerializer.Serialize(deviceStatus.Registers),
                Timestamp = deviceStatus.LastUpdate,
                Quality = deviceStatus.Quality
            };
            
            context.DeviceHistories.Add(historyRecord);
            await context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"记录设备 {deviceStatus.DeviceId} 历史数据失败");
        }
    }
}
```

#### 3.2.2 配方下发服务

```csharp
/// <summary>
/// 配方写入服务 - 负责配方数据的批量下发
/// </summary>
public class RecipeWriteService : IRecipeWriteService
{
    private readonly IConnectionPool _connectionPool;
    private readonly ICacheService _cache;
    private readonly IRecipeRepository _recipeRepository;
    private readonly IHubContext<DeviceMonitorHub> _hubContext;
    private readonly ILogger<RecipeWriteService> _logger;
    private readonly ModbusConfig _config;
    
    public RecipeWriteService(
        IConnectionPool connectionPool,
        ICacheService cache,
        IRecipeRepository recipeRepository,
        IHubContext<DeviceMonitorHub> hubContext,
        ILogger<RecipeWriteService> logger,
        IOptions<ModbusConfig> config)
    {
        _connectionPool = connectionPool;
        _cache = cache;
        _recipeRepository = recipeRepository;
        _hubContext = hubContext;
        _logger = logger;
        _config = config.Value;
    }
    
    public async Task<WriteResult> ExecuteRecipeAsync(ExecuteRecipeRequest request)
    {
        var taskId = Guid.NewGuid().ToString();
        _logger.LogInformation($"开始执行配方下发任务 {taskId}，配方ID: {request.RecipeId}，目标设备: {string.Join(",", request.DeviceIds)}");
        
        try
        {
            // 1. 验证配方和设备
            var recipe = await _recipeRepository.GetByIdAsync(request.RecipeId);
            if (recipe == null)
                throw new BusinessException($"配方 {request.RecipeId} 不存在");
            
            var devices = await ValidateDevicesAsync(request.DeviceIds);
            
            // 2. 创建写入任务
            var writeTask = new WriteTaskDto
            {
                TaskId = taskId,
                RecipeId = request.RecipeId,
                RecipeName = recipe.Name,
                DeviceIds = request.DeviceIds,
                TotalDevices = request.DeviceIds.Count,
                TotalRegisters = GetRegisterCount(recipe.Content),
                Status = WriteTaskStatus.Running,
                StartTime = DateTime.UtcNow,
                Progress = 0,
                CreatedBy = request.UserId
            };
            
            // 3. 标记设备为写入状态
            await MarkDevicesAsWritingAsync(request.DeviceIds, taskId);
            
            // 4. 异步执行写入任务
            _ = Task.Run(async () => await ExecuteWriteTaskAsync(writeTask, recipe, devices));
            
            return new WriteResult
            {
                Success = true,
                TaskId = taskId,
                Message = "配方下发任务已启动"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"启动配方下发任务失败: {ex.Message}");
            return new WriteResult
            {
                Success = false,
                Error = ex.Message
            };
        }
    }
    
    private async Task ExecuteWriteTaskAsync(WriteTaskDto writeTask, Recipe recipe, List<Device> devices)
    {
        try
        {
            // 解析配方内容
            var recipeData = JsonSerializer.Deserialize<RecipeData>(recipe.Content);
            
            // 按设备分组执行写入
            var completedDevices = 0;
            var totalDevices = devices.Count;
            
            foreach (var device in devices)
            {
                try
                {
                    await WriteRecipeToDeviceAsync(writeTask.TaskId, device, recipeData);
                    completedDevices++;
                    
                    // 更新进度
                    var progress = (double)completedDevices / totalDevices * 100;
                    await UpdateTaskProgressAsync(writeTask.TaskId, progress, $"已完成设备 {device.Name}");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"设备 {device.Name} 写入失败: {ex.Message}");
                    await UpdateTaskProgressAsync(writeTask.TaskId, null, $"设备 {device.Name} 写入失败: {ex.Message}");
                }
            }
            
            // 最终验证写入结果
            var verificationResult = await VerifyWriteResultAsync(devices, recipeData);
            
            if (verificationResult.Success)
            {
                await CompleteTaskAsync(writeTask.TaskId, WriteTaskStatus.Completed, "配方下发成功");
            }
            else
            {
                await CompleteTaskAsync(writeTask.TaskId, WriteTaskStatus.PartialSuccess, 
                    $"部分设备写入成功，失败设备: {string.Join(",", verificationResult.FailedDevices)}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"执行配方下发任务 {writeTask.TaskId} 失败");
            await CompleteTaskAsync(writeTask.TaskId, WriteTaskStatus.Failed, ex.Message);
        }
        finally
        {
            // 清除设备写入状态标记
            await ClearDeviceWritingStatusAsync(writeTask.DeviceIds);
        }
    }
    
    private async Task WriteRecipeToDeviceAsync(string taskId, Device device, RecipeData recipeData)
    {
        const int batchSize = 200; // 每批写入200个寄存器
        var connection = await _connectionPool.GetWriteConnectionAsync(device.IpAddress);
        
        try
        {
            var registers = recipeData.GetRegistersForDevice(device.Id);
            var batches = registers.Chunk(batchSize).ToList();
            
            for (int batchIndex = 0; batchIndex < batches.Count; batchIndex++)
            {
                var batch = batches[batchIndex];
                
                // 批量写入寄存器
                var addresses = batch.Select(r => r.Address).ToArray();
                var values = batch.Select(r => (ushort)r.Value).ToArray();
                
                await connection.WriteMultipleRegistersAsync(device.SlaveId, addresses[0], values);
                
                // 小批次之间短暂延迟，避免设备过载
                if (batchIndex < batches.Count - 1)
                {
                    await Task.Delay(50); // 50ms延迟
                }
                
                _logger.LogTrace($"设备 {device.Name} 批次 {batchIndex + 1}/{batches.Count} 写入完成");
            }
            
            _logger.LogInformation($"设备 {device.Name} 配方写入完成，共写入 {registers.Count} 个寄存器");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"设备 {device.Name} 配方写入失败");
            throw;
        }
    }
    
    private async Task UpdateTaskProgressAsync(string taskId, double? progress, string message)
    {
        var updateData = new
        {
            TaskId = taskId,
            Progress = progress,
            Message = message,
            UpdateTime = DateTime.UtcNow
        };
        
        // 更新缓存中的任务状态
        await _cache.SetAsync($"task:{taskId}:progress", updateData, TimeSpan.FromHours(1));
        
        // 通过SignalR推送进度更新
        await _hubContext.Clients.All.SendAsync("TaskProgressUpdate", updateData);
    }
    
    private async Task<VerificationResult> VerifyWriteResultAsync(List<Device> devices, RecipeData recipeData)
    {
        var result = new VerificationResult { Success = true, FailedDevices = new List<string>() };
        
        foreach (var device in devices)
        {
            try
            {
                var connection = await _connectionPool.GetReadConnectionAsync(device.IpAddress);
                var expectedRegisters = recipeData.GetRegistersForDevice(device.Id);
                
                // 读取设备当前寄存器值
                var actualValues = await connection.ReadHoldingRegistersAsync(
                    device.SlaveId,
                    expectedRegisters.First().Address,
                    expectedRegisters.Count);
                
                // 验证写入结果
                for (int i = 0; i < expectedRegisters.Count; i++)
                {
                    if (actualValues[i] != expectedRegisters[i].Value)
                    {
                        result.Success = false;
                        result.FailedDevices.Add(device.Name);
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"验证设备 {device.Name} 写入结果失败: {ex.Message}");
                result.Success = false;
                result.FailedDevices.Add(device.Name);
            }
        }
        
        return result;
    }
}
```

#### 3.2.3 连接池管理服务

```csharp
/// <summary>
/// Modbus连接池服务 - 管理设备连接的创建、复用和释放
/// </summary>
public class ConnectionPoolService : IConnectionPool, IDisposable
{
    private readonly ConcurrentBag<PooledConnection> _readPool;
    private readonly ConcurrentBag<PooledConnection> _writePool;
    private readonly ConcurrentDictionary<string, DateTime> _lastUsed;
    private readonly ConcurrentDictionary<string, SemaphoreSlim> _ipSemaphores;
    private readonly ModbusConfig _config;
    private readonly ILogger<ConnectionPoolService> _logger;
    private readonly Timer _cleanupTimer;
    private bool _disposed;
    
    public ConnectionPoolService(
        IOptions<ModbusConfig> config,
        ILogger<ConnectionPoolService> logger)
    {
        _config = config.Value;
        _logger = logger;
        _readPool = new ConcurrentBag<PooledConnection>();
        _writePool = new ConcurrentBag<PooledConnection>();
        _lastUsed = new ConcurrentDictionary<string, DateTime>();
        _ipSemaphores = new ConcurrentDictionary<string, SemaphoreSlim>();
        
        // 每分钟清理一次空闲连接
        _cleanupTimer = new Timer(CleanupIdleConnections, null, 
            TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        
        _logger.LogInformation($"连接池服务初始化完成，读连接池大小: {_config.MaxReadConnections}，写连接池大小: {_config.MaxWriteConnections}");
    }
    
    public async Task<IModbusConnection> GetReadConnectionAsync(string ipAddress)
    {
        return await GetConnectionAsync(ipAddress, _readPool, _config.MaxReadConnections, "Read");
    }
    
    public async Task<IModbusConnection> GetWriteConnectionAsync(string ipAddress)
    {
        return await GetConnectionAsync(ipAddress, _writePool, _config.MaxWriteConnections, "Write");
    }
    
    private async Task<IModbusConnection> GetConnectionAsync(
        string ipAddress, 
        ConcurrentBag<PooledConnection> pool,
        int maxConnections,
        string poolType)
    {
        // 每个IP地址的并发连接限制
        var semaphore = _ipSemaphores.GetOrAdd(ipAddress, _ => new SemaphoreSlim(5, 5));
        await semaphore.WaitAsync();
        
        try
        {
            // 尝试从池中获取可用连接
            var availableConnections = pool.Where(p => 
                p.IpAddress == ipAddress && 
                p.IsAlive && 
                !p.InUse).ToList();
            
            if (availableConnections.Any())
            {
                var pooled = availableConnections.First();
                pooled.InUse = true;
                _lastUsed[pooled.Id] = DateTime.UtcNow;
                
                _logger.LogTrace($"从 {poolType} 连接池获取到连接 {pooled.Id} ({ipAddress})");
                return new PooledConnectionWrapper(pooled, this);
            }
            
            // 检查池大小限制
            var currentPoolSize = pool.Count(p => p.IpAddress == ipAddress);
            if (currentPoolSize >= maxConnections)
            {
                // 等待连接可用
                for (int i = 0; i < 10; i++) // 最多等待1秒
                {
                    await Task.Delay(100);
                    
                    var waitingConnections = pool.Where(p => 
                        p.IpAddress == ipAddress && 
                        p.IsAlive && 
                        !p.InUse).ToList();
                        
                    if (waitingConnections.Any())
                    {
                        var pooled = waitingConnections.First();
                        pooled.InUse = true;
                        _lastUsed[pooled.Id] = DateTime.UtcNow;
                        return new PooledConnectionWrapper(pooled, this);
                    }
                }
                
                throw new InvalidOperationException($"无法获取到 {ipAddress} 的 {poolType} 连接，连接池已满");
            }
            
            // 创建新连接
            var connection = await CreateConnectionAsync(ipAddress);
            var pooledConnection = new PooledConnection
            {
                Id = Guid.NewGuid().ToString(),
                IpAddress = ipAddress,
                Connection = connection,
                CreatedAt = DateTime.UtcNow,
                InUse = true,
                PoolType = poolType
            };
            
            pool.Add(pooledConnection);
            _lastUsed[pooledConnection.Id] = DateTime.UtcNow;
            
            _logger.LogDebug($"创建新的 {poolType} 连接 {pooledConnection.Id} ({ipAddress})");
            return new PooledConnectionWrapper(pooledConnection, this);
        }
        finally
        {
            semaphore.Release();
        }
    }
    
    private async Task<IModbusConnection> CreateConnectionAsync(string ipAddress)
    {
        try
        {
            var connection = new ModbusTcpClient(ipAddress, 502);
            await connection.ConnectAsync(_config.ConnectionTimeout);
            
            _logger.LogTrace($"成功创建到 {ipAddress} 的Modbus连接");
            return connection;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"创建到 {ipAddress} 的Modbus连接失败");
            throw;
        }
    }
    
    public void ReturnConnection(PooledConnection pooledConnection)
    {
        if (pooledConnection != null && pooledConnection.IsAlive)
        {
            pooledConnection.InUse = false;
            _lastUsed[pooledConnection.Id] = DateTime.UtcNow;
            
            _logger.LogTrace($"连接 {pooledConnection.Id} 已归还到连接池");
        }
    }
    
    private void CleanupIdleConnections(object state)
    {
        try
        {
            var cutoffTime = DateTime.UtcNow.AddMinutes(-_config.IdleTimeoutMinutes);
            var connectionsToRemove = new List<PooledConnection>();
            
            // 检查读连接池
            CleanupPool(_readPool, cutoffTime, connectionsToRemove);
            
            // 检查写连接池  
            CleanupPool(_writePool, cutoffTime, connectionsToRemove);
            
            if (connectionsToRemove.Any())
            {
                _logger.LogInformation($"清理了 {connectionsToRemove.Count} 个空闲连接");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理空闲连接时发生异常");
        }
    }
    
    private void CleanupPool(ConcurrentBag<PooledConnection> pool, DateTime cutoffTime, List<PooledConnection> toRemove)
    {
        var allConnections = pool.ToArray();
        var newBag = new ConcurrentBag<PooledConnection>();
        
        foreach (var conn in allConnections)
        {
            var lastUsed = _lastUsed.GetValueOrDefault(conn.Id, conn.CreatedAt);
            
            if (!conn.InUse && lastUsed < cutoffTime)
            {
                // 连接空闲时间过长，释放
                conn.Connection?.Dispose();
                _lastUsed.TryRemove(conn.Id, out _);
                toRemove.Add(conn);
            }
            else if (conn.IsAlive)
            {
                // 保留活跃连接
                newBag.Add(conn);
            }
            else
            {
                // 移除死连接
                _lastUsed.TryRemove(conn.Id, out _);
                toRemove.Add(conn);
            }
        }
        
        // 更新连接池（注意：这里简化处理，实际需要更安全的替换方式）
        pool = newBag;
    }
}
```