using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Options;
using IndustryMonitor.Shared.Models.Configs;
using IndustryMonitor.Server.Data.Repositories;
using IndustryMonitor.Server.Services.Device;
using IndustryMonitor.Server.Services.Cache;
using IndustryMonitor.Server.Hubs;
using IndustryMonitor.Shared.Models.Entities;
using IndustryMonitor.Shared.Enums;
using IndustryMonitor.Shared.Models.DTOs;
using System.Collections.Concurrent;
using System.Text.Json;

namespace IndustryMonitor.Server.Services.Recipe;

/// <summary>
/// 配方下发服务接口
/// </summary>
public interface IRecipeWriteService
{
    Task<WriteTaskDto> CreateWriteTaskAsync(CreateWriteTaskRequest request);
    Task<WriteTaskDto?> GetWriteTaskAsync(string taskId);
    Task<IEnumerable<WriteTaskDto>> GetWriteTasksByStatusAsync(WriteTaskStatus status);
    Task<bool> CancelWriteTaskAsync(string taskId, string canceledBy);
    Task<WriteTaskProgressDto> GetWriteTaskProgressAsync(string taskId);
    Task ExecuteWriteTaskAsync(string taskId);
    Task<IEnumerable<WriteTaskDto>> GetRecentWriteTasksAsync(int count = 50);
}

/// <summary>
/// 配方下发服务实现
/// </summary>
public class RecipeWriteService : IRecipeWriteService
{
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly IConnectionPool _connectionPool;
    private readonly ICacheService _cacheService;
    private readonly IHubContext<DeviceMonitorHub> _hubContext;
    private readonly IHubContext<NotificationHub> _notificationHub;
    private readonly ModbusConfig _config;
    private readonly ILogger<RecipeWriteService> _logger;

    // 正在执行的任务
    private readonly ConcurrentDictionary<string, CancellationTokenSource> _runningTasks = new();
    
    public RecipeWriteService(
        IServiceScopeFactory scopeFactory,
        IConnectionPool connectionPool,
        ICacheService cacheService,
        IHubContext<DeviceMonitorHub> hubContext,
        IHubContext<NotificationHub> notificationHub,
        IOptions<ModbusConfig> config,
        ILogger<RecipeWriteService> logger)
    {
        _scopeFactory = scopeFactory;
        _connectionPool = connectionPool;
        _cacheService = cacheService;
        _hubContext = hubContext;
        _notificationHub = notificationHub;
        _config = config.Value;
        _logger = logger;
    }

    public async Task<WriteTaskDto> CreateWriteTaskAsync(CreateWriteTaskRequest request)
    {
        using var scope = _scopeFactory.CreateScope();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

        try
        {
            // 验证配方是否存在且未锁定
            var recipe = await unitOfWork.Recipes.GetByIdAsync(request.RecipeId);
            if (recipe == null)
            {
                throw new InvalidOperationException($"配方不存在: RecipeId = {request.RecipeId}");
            }

            if (recipe.IsLocked && recipe.LockedBy != request.CreatedBy)
            {
                throw new InvalidOperationException($"配方被其他用户锁定: {recipe.LockedBy}");
            }

            // 验证目标设备
            var targetDevices = await ValidateTargetDevicesAsync(unitOfWork, request.TargetDeviceIds);

            // 创建写入任务
            var writeTask = new WriteTask
            {
                TaskId = Guid.NewGuid().ToString(),
                RecipeId = request.RecipeId,
                TargetDeviceIds = JsonSerializer.Serialize(request.TargetDeviceIds),
                Status = WriteTaskStatus.Pending,
                TotalDevices = request.TargetDeviceIds.Count,
                CompletedDevices = 0,
                FailedDevices = 0,
                CreatedBy = request.CreatedBy,
                CreatedAt = DateTime.UtcNow,
                Priority = request.Priority ?? TaskPriority.Normal
            };

            var createdTask = await unitOfWork.WriteTasks.AddAsync(writeTask);
            await unitOfWork.SaveChangesAsync();

            _logger.LogInformation("创建配方下发任务: {TaskId}, 配方: {RecipeName}, 目标设备数: {DeviceCount}", 
                writeTask.TaskId, recipe.Name, request.TargetDeviceIds.Count);

            // 缓存任务进度信息
            await CacheTaskProgressAsync(writeTask, targetDevices, recipe);

            // 发送任务创建通知
            await SendTaskNotificationAsync(writeTask, "TaskCreated", "配方下发任务已创建");

            return MapToDto(createdTask, recipe);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建配方下发任务失败");
            throw;
        }
    }

    public async Task<WriteTaskDto?> GetWriteTaskAsync(string taskId)
    {
        using var scope = _scopeFactory.CreateScope();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

        try
        {
            var task = await unitOfWork.WriteTasks.GetByTaskIdAsync(taskId);
            return task != null ? MapToDto(task, task.Recipe) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取写入任务失败: TaskId = {TaskId}", taskId);
            throw;
        }
    }

    public async Task<IEnumerable<WriteTaskDto>> GetWriteTasksByStatusAsync(WriteTaskStatus status)
    {
        using var scope = _scopeFactory.CreateScope();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

        try
        {
            var tasks = await unitOfWork.WriteTasks.GetTasksByStatusAsync(status);
            return tasks.Select(t => MapToDto(t, t.Recipe));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "按状态获取写入任务失败: Status = {Status}", status);
            throw;
        }
    }

    public async Task<bool> CancelWriteTaskAsync(string taskId, string canceledBy)
    {
        using var scope = _scopeFactory.CreateScope();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

        try
        {
            var task = await unitOfWork.WriteTasks.GetByTaskIdAsync(taskId);
            if (task == null)
                return false;

            if (task.Status != WriteTaskStatus.Pending && task.Status != WriteTaskStatus.Running)
            {
                throw new InvalidOperationException($"任务状态为 {task.Status}，无法取消");
            }

            // 取消正在运行的任务
            if (_runningTasks.TryGetValue(taskId, out var cancellationTokenSource))
            {
                cancellationTokenSource.Cancel();
                _runningTasks.TryRemove(taskId, out _);
            }

            // 更新任务状态
            task.Status = WriteTaskStatus.Cancelled;
            task.EndTime = DateTime.UtcNow;
            task.ErrorMessage = $"任务被 {canceledBy} 取消";

            await unitOfWork.WriteTasks.UpdateAsync(task);
            await unitOfWork.SaveChangesAsync();

            // 发送取消通知
            await SendTaskNotificationAsync(task, "TaskCancelled", $"配方下发任务已被取消: {canceledBy}");

            _logger.LogInformation("取消配方下发任务: {TaskId} by {CancelledBy}", taskId, canceledBy);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消写入任务失败: TaskId = {TaskId}", taskId);
            throw;
        }
    }

    public async Task<WriteTaskProgressDto> GetWriteTaskProgressAsync(string taskId)
    {
        try
        {
            var progressKey = $"write_task_progress_{taskId}";
            var progress = await _cacheService.GetAsync<WriteTaskProgressDto>(progressKey);
            
            if (progress == null)
            {
                // 从数据库获取基本信息
                using var scope = _scopeFactory.CreateScope();
                var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
                var task = await unitOfWork.WriteTasks.GetByTaskIdAsync(taskId);
                
                if (task != null)
                {
                    progress = new WriteTaskProgressDto
                    {
                        TaskId = taskId,
                        Status = task.Status,
                        TotalDevices = task.TotalDevices,
                        CompletedDevices = task.CompletedDevices,
                        FailedDevices = task.FailedDevices,
                        ProgressPercentage = task.TotalDevices > 0 ? 
                            (double)(task.CompletedDevices + task.FailedDevices) / task.TotalDevices * 100 : 0,
                        DeviceResults = new List<DeviceWriteResult>(),
                        StartTime = task.StartTime,
                        EndTime = task.EndTime,
                        ErrorMessage = task.ErrorMessage
                    };
                }
            }

            return progress ?? new WriteTaskProgressDto { TaskId = taskId };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取任务进度失败: TaskId = {TaskId}", taskId);
            throw;
        }
    }

    public async Task<IEnumerable<WriteTaskDto>> GetRecentWriteTasksAsync(int count = 50)
    {
        using var scope = _scopeFactory.CreateScope();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

        try
        {
            var tasks = await unitOfWork.WriteTasks.GetRecentTasksAsync(count);
            return tasks.Select(t => MapToDto(t, t.Recipe));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取最近写入任务失败");
            throw;
        }
    }

    public async Task ExecuteWriteTaskAsync(string taskId)
    {
        using var scope = _scopeFactory.CreateScope();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

        try
        {
            var task = await unitOfWork.WriteTasks.GetByTaskIdAsync(taskId);
            if (task == null)
            {
                throw new InvalidOperationException($"写入任务不存在: {taskId}");
            }

            if (task.Status != WriteTaskStatus.Pending)
            {
                throw new InvalidOperationException($"任务状态为 {task.Status}，无法执行");
            }

            // 创建取消令牌
            var cancellationTokenSource = new CancellationTokenSource();
            _runningTasks[taskId] = cancellationTokenSource;

            // 启动后台任务执行
            _ = Task.Run(async () => await ExecuteWriteTaskInternalAsync(task, cancellationTokenSource.Token));

            _logger.LogInformation("开始执行配方下发任务: {TaskId}", taskId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动写入任务执行失败: TaskId = {TaskId}", taskId);
            throw;
        }
    }

    private async Task ExecuteWriteTaskInternalAsync(WriteTask task, CancellationToken cancellationToken)
    {
        using var scope = _scopeFactory.CreateScope();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

        try
        {
            // 更新任务状态为运行中
            task.Status = WriteTaskStatus.Running;
            task.StartTime = DateTime.UtcNow;
            await unitOfWork.WriteTasks.UpdateAsync(task);
            await unitOfWork.SaveChangesAsync();

            await SendTaskNotificationAsync(task, "TaskStarted", "配方下发任务开始执行");

            // 获取配方和目标设备
            var recipe = await unitOfWork.Recipes.GetByIdAsync(task.RecipeId);
            var targetDeviceIds = JsonSerializer.Deserialize<List<int>>(task.TargetDeviceIds) ?? new List<int>();
            var targetDevices = new List<IndustryMonitor.Shared.Models.Entities.Device>();
            
            foreach (var deviceId in targetDeviceIds)
            {
                var device = await unitOfWork.Devices.GetByIdAsync(deviceId);
                if (device != null)
                    targetDevices.Add(device);
            }

            // 解析配方内容
            var recipeData = ParseRecipeContent(recipe!.Content);
            
            // 初始化进度跟踪
            var progress = new WriteTaskProgressDto
            {
                TaskId = task.TaskId,
                Status = WriteTaskStatus.Running,
                TotalDevices = targetDevices.Count,
                CompletedDevices = 0,
                FailedDevices = 0,
                DeviceResults = new List<DeviceWriteResult>(),
                StartTime = task.StartTime
            };

            await UpdateTaskProgressAsync(progress);

            // 并行写入设备（限制并发数）
            var semaphore = new SemaphoreSlim(_config.MaxConcurrentWrites, _config.MaxConcurrentWrites);
            var writeTasks = targetDevices.Select(device => 
                WriteToDeviceAsync(device, recipeData, progress, semaphore, cancellationToken));

            await Task.WhenAll(writeTasks);

            // 更新最终状态
            task.Status = progress.FailedDevices > 0 ? WriteTaskStatus.PartialSuccess : WriteTaskStatus.Completed;
            task.CompletedDevices = progress.CompletedDevices;
            task.FailedDevices = progress.FailedDevices;
            task.EndTime = DateTime.UtcNow;

            await unitOfWork.WriteTasks.UpdateAsync(task);
            await unitOfWork.SaveChangesAsync();

            progress.Status = task.Status;
            progress.EndTime = task.EndTime;
            await UpdateTaskProgressAsync(progress);

            await SendTaskNotificationAsync(task, "TaskCompleted", 
                $"配方下发任务完成: 成功 {progress.CompletedDevices}/{progress.TotalDevices}");

            _logger.LogInformation("配方下发任务执行完成: {TaskId}, 成功: {Success}, 失败: {Failed}", 
                task.TaskId, progress.CompletedDevices, progress.FailedDevices);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("配方下发任务被取消: {TaskId}", task.TaskId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "配方下发任务执行失败: {TaskId}", task.TaskId);
            
            task.Status = WriteTaskStatus.Failed;
            task.EndTime = DateTime.UtcNow;
            task.ErrorMessage = ex.Message;
            
            await unitOfWork.WriteTasks.UpdateAsync(task);
            await unitOfWork.SaveChangesAsync();

            await SendTaskNotificationAsync(task, "TaskFailed", $"配方下发任务失败: {ex.Message}");
        }
        finally
        {
            _runningTasks.TryRemove(task.TaskId, out _);
        }
    }

    private async Task WriteToDeviceAsync(
        IndustryMonitor.Shared.Models.Entities.Device device, 
        Dictionary<int, ushort> recipeData, 
        WriteTaskProgressDto progress,
        SemaphoreSlim semaphore, 
        CancellationToken cancellationToken)
    {
        await semaphore.WaitAsync(cancellationToken);
        
        try
        {
            var result = new DeviceWriteResult
            {
                DeviceId = device.Id,
                DeviceName = device.Name,
                IpAddress = device.IpAddress,
                StartTime = DateTime.UtcNow
            };

            IModbusConnection? connection = null;
            try
            {
                connection = await _connectionPool.GetWriteConnectionAsync(device.IpAddress);
                
                // 按地址排序并分组写入
                var sortedData = recipeData.OrderBy(kv => kv.Key).ToList();
                var writeGroups = GroupConsecutiveAddresses(sortedData);

                foreach (var (startAddress, values) in writeGroups)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    await connection.WriteMultipleRegistersAsync(_config.SlaveId, startAddress, values);
                    
                    // 添加写入延迟，避免设备过载
                    if (_config.WriteDelayMs > 0)
                    {
                        await Task.Delay(_config.WriteDelayMs, cancellationToken);
                    }
                }

                result.Success = true;
                result.Message = $"成功写入 {recipeData.Count} 个寄存器";
                
                lock (progress)
                {
                    progress.CompletedDevices++;
                }

                _logger.LogTrace("设备写入成功: {DeviceName} ({IpAddress})", device.Name, device.IpAddress);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = ex.Message;
                
                lock (progress)
                {
                    progress.FailedDevices++;
                }

                _logger.LogWarning(ex, "设备写入失败: {DeviceName} ({IpAddress})", device.Name, device.IpAddress);
            }
            finally
            {
                connection?.Dispose();
                result.EndTime = DateTime.UtcNow;
            }

            lock (progress.DeviceResults)
            {
                progress.DeviceResults.Add(result);
            }

            // 更新进度
            progress.ProgressPercentage = (double)(progress.CompletedDevices + progress.FailedDevices) / progress.TotalDevices * 100;
            await UpdateTaskProgressAsync(progress);
        }
        finally
        {
            semaphore.Release();
        }
    }

    private Dictionary<int, ushort> ParseRecipeContent(string content)
    {
        try
        {
            var jsonDoc = JsonDocument.Parse(content);
            var registerData = new Dictionary<int, ushort>();

            if (jsonDoc.RootElement.TryGetProperty("registers", out var registersElement))
            {
                foreach (var register in registersElement.EnumerateArray())
                {
                    if (register.TryGetProperty("address", out var addressElement) &&
                        register.TryGetProperty("value", out var valueElement))
                    {
                        var address = addressElement.GetInt32();
                        var value = (ushort)valueElement.GetInt32();
                        registerData[address] = value;
                    }
                }
            }

            return registerData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析配方内容失败");
            throw new InvalidOperationException("配方内容格式错误", ex);
        }
    }

    private List<(int startAddress, ushort[] values)> GroupConsecutiveAddresses(List<KeyValuePair<int, ushort>> sortedData)
    {
        var groups = new List<(int startAddress, ushort[] values)>();
        if (!sortedData.Any()) return groups;

        var currentGroup = new List<ushort> { sortedData[0].Value };
        var currentStartAddress = sortedData[0].Key;

        for (int i = 1; i < sortedData.Count; i++)
        {
            if (sortedData[i].Key == currentStartAddress + currentGroup.Count)
            {
                // 连续地址，添加到当前组
                currentGroup.Add(sortedData[i].Value);
            }
            else
            {
                // 非连续地址，完成当前组
                groups.Add((currentStartAddress, currentGroup.ToArray()));
                currentGroup = new List<ushort> { sortedData[i].Value };
                currentStartAddress = sortedData[i].Key;
            }
        }

        // 添加最后一组
        groups.Add((currentStartAddress, currentGroup.ToArray()));
        return groups;
    }

    private async Task<List<IndustryMonitor.Shared.Models.Entities.Device>> ValidateTargetDevicesAsync(
        IUnitOfWork unitOfWork, List<int> deviceIds)
    {
        var devices = new List<IndustryMonitor.Shared.Models.Entities.Device>();
        
        foreach (var deviceId in deviceIds)
        {
            var device = await unitOfWork.Devices.GetByIdAsync(deviceId);
            if (device == null)
            {
                throw new InvalidOperationException($"设备不存在: DeviceId = {deviceId}");
            }
            
            if (!device.IsActive)
            {
                throw new InvalidOperationException($"设备未激活: {device.Name}");
            }
            
            devices.Add(device);
        }

        return devices;
    }

    private async Task CacheTaskProgressAsync(
        WriteTask task, 
        List<IndustryMonitor.Shared.Models.Entities.Device> devices, 
        IndustryMonitor.Shared.Models.Entities.Recipe recipe)
    {
        var progress = new WriteTaskProgressDto
        {
            TaskId = task.TaskId,
            Status = task.Status,
            TotalDevices = devices.Count,
            CompletedDevices = 0,
            FailedDevices = 0,
            DeviceResults = new List<DeviceWriteResult>(),
            RecipeName = recipe.Name,
            CreatedBy = task.CreatedBy,
            CreatedAt = task.CreatedAt
        };

        await UpdateTaskProgressAsync(progress);
    }

    private async Task UpdateTaskProgressAsync(WriteTaskProgressDto progress)
    {
        try
        {
            var progressKey = $"write_task_progress_{progress.TaskId}";
            await _cacheService.SetAsync(progressKey, progress, TimeSpan.FromHours(24));

            // 发送实时进度更新
            await _hubContext.Clients.All.SendAsync("WriteTaskProgress", progress);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "更新任务进度缓存失败: TaskId = {TaskId}", progress.TaskId);
        }
    }

    private async Task SendTaskNotificationAsync(WriteTask task, string type, string message)
    {
        try
        {
            var notification = new
            {
                Type = type,
                TaskId = task.TaskId,
                Message = message,
                Status = task.Status.ToString(),
                CreatedBy = task.CreatedBy,
                Timestamp = DateTime.UtcNow
            };

            await _notificationHub.Clients.All.SendAsync("WriteTaskNotification", notification);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "发送任务通知失败: TaskId = {TaskId}", task.TaskId);
        }
    }

    private static WriteTaskDto MapToDto(WriteTask task, IndustryMonitor.Shared.Models.Entities.Recipe? recipe)
    {
        return new WriteTaskDto
        {
            TaskId = task.TaskId,
            RecipeId = task.RecipeId,
            RecipeName = recipe?.Name ?? "未知配方",
            Status = task.Status,
            TotalDevices = task.TotalDevices,
            CompletedDevices = task.CompletedDevices,
            FailedDevices = task.FailedDevices,
            Priority = task.Priority,
            CreatedBy = task.CreatedBy,
            CreatedAt = task.CreatedAt,
            StartTime = task.StartTime,
            EndTime = task.EndTime,
            ErrorMessage = task.ErrorMessage
        };
    }
}