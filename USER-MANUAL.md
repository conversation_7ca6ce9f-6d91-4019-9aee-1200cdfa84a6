# 用户操作手册

欢迎使用工业设备监控与配方下发系统！本手册将指导您如何使用系统的各项功能。

## 📋 目录

1. [系统介绍](#1-系统介绍)
2. [首次使用](#2-首次使用)
3. [设备监控](#3-设备监控)
4. [配方管理](#4-配方管理)
5. [任务执行](#5-任务执行)
6. [系统管理](#6-系统管理)
7. [常见问题](#7-常见问题)
8. [故障排除](#8-故障排除)

---

## 1. 系统介绍

### 1.1 系统概述

工业设备监控与配方下发系统是一套专业的工业自动化管理软件，主要功能包括：

- **实时监控**: 监控200台PLC设备的运行状态和数据
- **配方管理**: 创建、编辑、管理生产配方
- **批量下发**: 将配方批量下发到指定设备
- **数据记录**: 记录设备历史数据和操作日志
- **用户管理**: 多用户角色权限管理

### 1.2 用户角色说明

| 角色 | 权限说明 |
|------|----------|
| **管理员** | 拥有所有权限，可以管理用户、系统配置 |
| **操作员** | 可以监控设备、管理配方、执行下发任务 |
| **查看者** | 仅可查看设备状态和历史数据，无修改权限 |

### 1.3 系统界面概览

系统采用现代化的Material Design设计风格，界面简洁直观：

```
┌─────────────────────────────────────────────────────────────┐
│ 工业设备监控系统                              用户: admin ▼ │
├─────────────────────────────────────────────────────────────┤
│ 📊 设备监控 │ 📋 配方管理 │ ⚡ 任务中心 │ ⚙️ 系统管理 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                    主要工作区域                              │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│ 状态栏: 连接状态 │ 在线设备 │ 当前任务 │ 系统时间          │
└─────────────────────────────────────────────────────────────┘
```

---

## 2. 首次使用

### 2.1 系统登录

1. 双击桌面上的"工业设备监控系统"图标启动程序
2. 在登录界面输入用户名和密码
3. 点击"登录"按钮

**默认管理员账户**：
- 用户名：`admin`
- 密码：`admin123`

> ⚠️ **安全提示**：首次登录后请立即修改默认密码！

![登录界面示例](images/login-screen.png)

### 2.2 主界面介绍

登录成功后，您将看到系统主界面：

#### 顶部导航栏
- **设备监控**：查看所有设备的实时状态
- **配方管理**：创建和管理生产配方
- **任务中心**：查看和管理下发任务
- **系统管理**：系统设置和用户管理（仅管理员可见）

#### 状态栏信息
- **连接状态**：显示与服务器的连接状态
- **在线设备**：当前在线设备数量
- **当前任务**：正在执行的任务数量
- **系统时间**：当前系统时间

### 2.3 修改密码

首次登录后强烈建议修改密码：

1. 点击右上角用户名下拉菜单
2. 选择"修改密码"
3. 输入当前密码和新密码
4. 点击"确认修改"

---

## 3. 设备监控

设备监控是系统的核心功能，用于实时查看所有PLC设备的运行状态。

### 3.1 设备状态总览

在"设备监控"页面，您可以看到：

#### 网格视图
设备以卡片形式展示，每个卡片包含：
- **设备名称**：如"1号注塑机"
- **IP地址**：设备的网络地址
- **状态指示**：绿色(在线)、红色(离线)、黄色(异常)
- **关键数据**：温度、压力等重要参数
- **更新时间**：最后数据更新时间

![设备监控网格视图](images/device-grid-view.png)

#### 列表视图
切换到列表视图可以看到更多详细信息：
- 设备编号、名称、位置
- IP地址和通信参数
- 当前状态和错误信息
- 寄存器数据一览

### 3.2 设备详情查看

点击任意设备卡片或列表项可以查看详细信息：

#### 基本信息
- 设备名称、型号、位置
- IP地址、端口、从站地址
- 寄存器范围配置

#### 实时数据
- 所有寄存器的当前值
- 数据质量和更新时间
- 历史趋势图表

#### 连接状态
- 连接持续时间
- 通信错误次数
- 最后连接时间

### 3.3 数据筛选和搜索

系统提供强大的筛选功能：

#### 状态筛选
- **全部**：显示所有设备
- **在线**：仅显示正常在线设备
- **离线**：显示断线设备
- **异常**：显示通信错误设备

#### 文本搜索
在搜索框中输入关键词可搜索：
- 设备名称
- IP地址
- 位置信息

#### 高级筛选
点击"高级筛选"可以按以下条件筛选：
- 设备类型
- 所在位置/车间
- 更新时间范围

### 3.4 历史数据查询

查看设备的历史运行数据：

1. 选择要查询的设备
2. 点击"历史数据"按钮
3. 设置时间范围（最近1小时/8小时/1天/1周）
4. 选择要查看的寄存器
5. 点击"查询"生成趋势图

#### 支持的图表类型
- **折线图**：显示数值随时间变化
- **柱状图**：对比不同时间段的数值
- **散点图**：分析数据分布规律

### 3.5 数据导出

您可以导出设备数据用于分析：

1. 选择要导出的设备（可多选）
2. 设置时间范围
3. 选择数据格式（Excel/CSV/PDF）
4. 点击"导出"按钮
5. 选择保存位置

---

## 4. 配方管理

配方管理用于创建、编辑和维护生产配方。

### 4.1 配方列表

在"配方管理"页面可以看到所有配方：

#### 配方信息
- **配方名称**：如"产品A标准配方"
- **版本号**：当前版本（如v1.3）
- **创建者**：配方创建人
- **创建时间**：配方创建日期
- **锁定状态**：是否被其他用户锁定编辑
- **寄存器数量**：配方包含的寄存器个数

#### 操作按钮
- **查看**：查看配方详细内容
- **编辑**：修改配方（需要先锁定）
- **复制**：基于当前配方创建副本
- **下发**：执行配方下发任务
- **导出**：导出配方文件

### 4.2 创建新配方

创建新的生产配方：

#### 步骤1：基本信息
1. 点击"新建配方"按钮
2. 输入配方名称（如"产品B标准配方"）
3. 输入配方描述
4. 设置版本号（默认1.0）

#### 步骤2：添加寄存器数据
1. 点击"添加寄存器"
2. 输入寄存器地址（如400001）
3. 设置寄存器值
4. 添加描述信息（可选）
5. 重复以上步骤添加更多寄存器

#### 步骤3：保存配方
1. 检查所有数据是否正确
2. 点击"保存"按钮
3. 系统自动分配配方ID

**配方数据示例**：
```
寄存器地址    数值     描述
400001       1800     设定温度(°C×10)
400002       850      设定压力(bar×10)  
400003       45       循环时间(秒)
400004       100      注射速度(%)
400005       80       保压压力(%)
```

### 4.3 编辑配方

修改现有配方：

#### 获取编辑权限
1. 在配方列表中找到要编辑的配方
2. 点击"编辑"按钮
3. 系统会尝试锁定配方（避免多人同时编辑）
4. 如果配方已被其他用户锁定，会显示锁定信息

#### 配方编辑器
成功锁定后进入编辑模式：
- **左侧**：寄存器列表，可以增删改寄存器
- **右侧**：预览区域，显示配方效果
- **底部**：保存、取消、预览按钮

#### 保存修改
1. 完成编辑后点击"保存"
2. 系统会自动升级版本号
3. 记录修改历史
4. 释放配方锁定

### 4.4 配方版本管理

系统自动管理配方版本：

#### 版本历史
- 每次修改都会创建新版本
- 保留所有历史版本
- 可以查看版本之间的差异
- 支持回退到历史版本

#### 查看版本历史
1. 选择配方
2. 点击"版本历史"
3. 查看所有历史版本
4. 对比不同版本的差异

### 4.5 配方导入导出

#### 导出配方
1. 选择要导出的配方
2. 点击"导出"按钮
3. 选择导出格式（JSON/Excel/XML）
4. 选择保存位置

#### 导入配方
1. 点击"导入配方"按钮
2. 选择配方文件
3. 预览导入内容
4. 确认导入

---

## 5. 任务执行

任务中心用于执行和监控配方下发任务。

### 5.1 创建下发任务

#### 启动新任务
1. 在配方管理页面选择要下发的配方
2. 点击"下发"按钮
3. 或在任务中心点击"新建任务"

#### 任务配置
配置下发任务参数：

**选择目标设备**：
- 可以单独选择设备
- 支持按区域/车间批量选择
- 支持按设备类型筛选
- 显示设备当前状态

**任务设置**：
- **任务名称**：为任务起个便于识别的名称
- **执行优先级**：高/中/低三个级别
- **超时时间**：单个设备的最大执行时间
- **失败处理**：遇到失败时是否继续其他设备

**确认下发**：
1. 检查配方内容和目标设备
2. 点击"开始执行"
3. 系统显示任务ID和开始时间

### 5.2 监控任务进度

任务启动后可以实时监控执行情况：

#### 总体进度
- **进度条**：显示整体完成百分比
- **状态信息**：当前正在执行的步骤
- **预计完成时间**：基于当前进度的时间估算
- **已用时间**：任务已经执行的时间

#### 设备级进度
查看每个设备的详细执行情况：
- **设备状态**：等待中/执行中/已完成/失败
- **进度百分比**：该设备的完成进度
- **写入寄存器数**：已成功写入的寄存器数量
- **当前寄存器**：正在写入的寄存器地址
- **错误信息**：如果失败，显示具体错误

#### 实时日志
在日志区域可以看到：
- 任务开始时间
- 每个设备的开始/完成时间
- 写入进度和状态变化
- 错误和警告信息

### 5.3 任务操作

任务执行过程中可以进行以下操作：

#### 暂停任务
1. 点击"暂停"按钮
2. 当前正在执行的设备会完成
3. 尚未开始的设备会被暂停
4. 可以点击"继续"恢复执行

#### 取消任务
1. 点击"取消"按钮
2. 确认取消操作
3. 已完成的设备不会回退
4. 任务状态变为"已取消"

#### 重试失败设备
如果部分设备执行失败：
1. 任务完成后点击"重试失败设备"
2. 系统会创建新的重试任务
3. 只对失败的设备重新执行

### 5.4 任务历史

查看历史任务记录：

#### 任务列表
- **任务ID**：唯一标识符
- **配方名称**：使用的配方
- **设备数量**：目标设备总数
- **执行状态**：成功/失败/部分成功/已取消
- **开始时间**：任务启动时间
- **完成时间**：任务结束时间
- **执行者**：任务创建人

#### 任务详情
点击任务可以查看详细信息：
- 完整的执行日志
- 每个设备的执行结果
- 成功/失败统计
- 耗时分析

#### 筛选和搜索
- 按时间范围筛选
- 按执行状态筛选
- 按执行人筛选
- 按配方名称搜索

---

## 6. 系统管理

系统管理功能仅对管理员角色开放。

### 6.1 用户管理

#### 用户列表
查看所有系统用户：
- **用户名**：登录账户名
- **姓名**：用户真实姓名
- **角色**：管理员/操作员/查看者
- **状态**：启用/禁用
- **最后登录**：最近登录时间
- **创建时间**：账户创建时间

#### 创建新用户
1. 点击"新建用户"按钮
2. 输入用户基本信息：
   - 用户名（英文，不可重复）
   - 姓名（中文真实姓名）
   - 密码（至少8位，包含数字和字母）
   - 确认密码
3. 选择用户角色
4. 点击"保存"创建用户

#### 编辑用户
1. 在用户列表中点击"编辑"
2. 可以修改：
   - 姓名
   - 角色
   - 启用/禁用状态
3. 保存修改

#### 重置密码
为用户重置密码：
1. 选择要重置的用户
2. 点击"重置密码"
3. 系统生成临时密码
4. 通知用户新密码（用户首次登录需要修改）

### 6.2 设备管理

#### 设备配置
管理系统中的设备：

**添加设备**：
1. 点击"添加设备"
2. 输入设备信息：
   - 设备名称
   - IP地址
   - 端口号（默认502）
   - 从站地址
   - 寄存器起始地址
   - 寄存器数量
   - 设备类型
   - 安装位置

**编辑设备**：
- 修改设备基本信息
- 调整通信参数
- 更改寄存器配置

**删除设备**：
- 只能删除离线状态的设备
- 删除前会警告数据丢失风险

#### 批量操作
- **批量启用/禁用**：快速管理多个设备
- **批量导入**：从Excel文件导入设备信息
- **批量导出**：导出设备配置用于备份

### 6.3 系统配置

#### 通信配置
调整Modbus通信参数：
- **最大读取连接数**：默认80
- **最大写入连接数**：默认20
- **连接超时时间**：默认3000ms
- **读取超时时间**：默认200ms
- **写入超时时间**：默认5000ms

#### 数据管理
- **数据保留天数**：历史数据保存时间
- **轮询间隔**：设备数据采集频率
- **备份频率**：自动备份周期

#### 安全设置
- **登录失败限制**：防暴力破解
- **会话超时时间**：自动登出时间
- **密码复杂度**：密码安全要求

### 6.4 系统监控

#### 实时状态
监控系统运行状态：
- **CPU使用率**：服务器CPU占用
- **内存使用量**：内存占用情况
- **磁盘空间**：存储空间使用
- **网络流量**：实时网络数据
- **活跃连接数**：当前设备连接数

#### 性能统计
- **数据采集统计**：成功/失败率
- **响应时间分析**：通信延迟统计
- **错误分析**：常见错误统计
- **任务执行统计**：任务成功率

### 6.5 日志管理

#### 操作日志
查看所有用户操作记录：
- **操作时间**：精确到秒
- **操作用户**：执行操作的用户
- **操作类型**：登录/设备管理/配方操作等
- **操作对象**：被操作的具体对象
- **操作结果**：成功/失败
- **详细信息**：操作详情描述

#### 系统日志
查看系统运行日志：
- **错误日志**：系统错误和异常
- **警告日志**：需要关注的警告信息
- **信息日志**：一般运行信息
- **调试日志**：详细的调试信息（可开启/关闭）

#### 日志导出
- 支持按时间范围导出
- 支持按日志级别筛选
- 导出格式：Excel、CSV、TXT

---

## 7. 常见问题

### 7.1 登录问题

#### Q: 忘记密码怎么办？
**A**: 请联系系统管理员为您重置密码。管理员可以在"用户管理"中重置任何用户的密码。

#### Q: 提示"用户名或密码错误"？
**A**: 
1. 检查用户名和密码是否正确（注意大小写）
2. 确认账户是否被禁用
3. 确认是否达到登录失败次数限制

#### Q: 长时间不操作会自动退出吗？
**A**: 是的，为了安全考虑，系统会在30分钟无操作后自动退出。可以在系统设置中调整此时间。

### 7.2 设备监控问题

#### Q: 设备显示离线状态？
**A**: 
1. 检查设备是否正常通电
2. 检查网络连接是否正常
3. 验证设备IP地址是否正确
4. 检查设备是否支持Modbus TCP协议

#### Q: 设备数据不更新？
**A**: 
1. 检查设备连接状态
2. 查看系统日志是否有通信错误
3. 重启设备或服务器
4. 联系技术支持

#### Q: 如何添加新设备？
**A**: 
1. 管理员登录系统
2. 进入"系统管理"->"设备管理"
3. 点击"添加设备"，填写设备信息
4. 保存后设备会自动开始监控

### 7.3 配方管理问题

#### Q: 无法编辑配方，提示被锁定？
**A**: 
- 配方正在被其他用户编辑
- 等待其他用户完成编辑，或联系管理员强制解锁
- 锁定时间默认30分钟，超时后自动解锁

#### Q: 如何批量修改多个配方？
**A**: 
- 系统不支持批量修改配方内容
- 可以使用"复制配方"功能基于现有配方创建新配方
- 或使用导入/导出功能进行批量处理

#### Q: 配方历史版本可以恢复吗？
**A**: 
- 可以查看历史版本内容
- 需要手动复制历史版本内容创建新版本
- 建议定期导出重要配方进行备份

### 7.4 任务执行问题

#### Q: 任务执行失败了怎么办？
**A**: 
1. 查看任务详情中的错误信息
2. 检查失败设备的连接状态
3. 使用"重试失败设备"功能
4. 如果问题持续，联系技术支持

#### Q: 可以同时执行多个任务吗？
**A**: 
- 系统默认最多同时执行5个任务
- 可以在系统设置中调整并发任务数量
- 建议不要设置过高以避免网络拥塞

#### Q: 任务取消后可以恢复吗？
**A**: 
- 已取消的任务无法直接恢复
- 可以基于同样的配方和设备创建新任务
- 已完成部分的设备不会回退

---

## 8. 故障排除

### 8.1 网络连接问题

#### 现象：无法连接服务器
**可能原因**：
- 服务器未启动
- 网络故障
- 防火墙阻挡
- 服务器地址错误

**解决方案**：
1. **检查服务器状态**
   ```
   ping 服务器IP地址
   telnet 服务器IP地址 5000
   ```

2. **检查客户端配置**
   - 打开安装目录下的`appsettings.json`
   - 确认`ServerUrl`配置正确
   - 重启客户端程序

3. **检查防火墙**
   - Windows防火墙是否阻挡5000端口
   - 企业防火墙设置
   - 路由器端口映射

#### 现象：连接频繁断开
**解决方案**：
1. 检查网络稳定性
2. 调整重连间隔设置
3. 升级网络设备驱动
4. 联系网络管理员检查网络质量

### 8.2 设备通信问题

#### 现象：设备显示通信错误
**诊断步骤**：
1. **使用Modbus测试工具验证**
   - 推荐使用ModbusPoll或QModMaster
   - 直接连接设备测试通信

2. **检查设备配置**
   - IP地址、端口、从站地址
   - 寄存器地址范围
   - 设备是否支持多连接

3. **查看通信日志**
   - 系统管理->日志管理
   - 搜索设备IP相关的错误信息

**常见解决方案**：
- 调整通信超时时间
- 减少并发连接数
- 检查网络延迟
- 重启设备和服务

### 8.3 性能问题

#### 现象：界面响应缓慢
**优化方案**：
1. **客户端优化**
   - 关闭不必要的程序
   - 增加系统内存
   - 更新显卡驱动

2. **服务端优化**
   - 检查CPU和内存使用率
   - 清理历史数据
   - 重启服务端程序

3. **网络优化**
   - 使用有线网络连接
   - 检查网络带宽占用
   - 优化网络设备配置

#### 现象：大量设备时系统卡顿
**解决方案**：
1. 分批显示设备（分页或虚拟化）
2. 调整数据刷新频率
3. 使用更高性能的服务器
4. 考虑分布式部署

### 8.4 数据问题

#### 现象：历史数据查询很慢
**优化方案**：
1. 缩小时间查询范围
2. 减少同时查询的寄存器数量
3. 定期清理过期数据
4. 考虑使用专业时序数据库

#### 现象：数据不准确
**检查项目**：
1. 设备寄存器配置是否正确
2. 数据类型和单位是否匹配
3. 设备时钟是否同步
4. 通信是否存在干扰

### 8.5 系统维护

#### 日常维护检查清单

**每日检查**：
- [ ] 系统服务运行状态
- [ ] 设备在线率
- [ ] 当日任务执行情况
- [ ] 磁盘空间使用率

**每周检查**：
- [ ] 系统日志分析
- [ ] 性能统计报告
- [ ] 数据备份验证
- [ ] 用户活动审计

**每月维护**：
- [ ] 清理过期日志文件
- [ ] 数据库备份和归档
- [ ] 系统更新检查
- [ ] 安全漏洞检查

#### 紧急联系方式

**技术支持热线**：400-XXX-XXXX
**邮箱支持**：<EMAIL>
**QQ技术群**：123456789
**微信技术支持**：IndustryMonitorSupport

---

## 结语

感谢您使用工业设备监控与配方下发系统！

本手册涵盖了系统的主要功能和常见操作。如果您在使用过程中遇到本手册未覆盖的问题，请及时联系我们的技术支持团队。

我们会持续改进系统功能，优化用户体验。您的反馈和建议对我们来说非常宝贵，欢迎随时与我们联系。

**系统版本**：v1.0.0  
**手册版本**：v1.0  
**更新日期**：2024年1月20日

---

> 💡 **提示**：建议将本手册保存到本地，以便离线查阅。系统界面的"帮助"按钮也可以快速访问相关文档。