using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using IndustryMonitor.Server.Services.Auth;
using IndustryMonitor.Shared.Models.DTOs;

namespace IndustryMonitor.Server.Controllers.Api.v1;

/// <summary>
/// 用户管理 API 控制器
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class UsersController : ControllerBase
{
    private readonly IAuthService _authService;
    private readonly ILogger<UsersController> _logger;

    public UsersController(IAuthService authService, ILogger<UsersController> logger)
    {
        _authService = authService;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有用户
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<IEnumerable<UserDto>>>> GetAllUsers()
    {
        try
        {
            var users = await _authService.GetAllUsersAsync();
            return Ok(ApiResponse<IEnumerable<UserDto>>.Success(users, "获取用户列表成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有用户失败");
            return StatusCode(500, ApiResponse<object>.Error("获取用户列表失败"));
        }
    }

    /// <summary>
    /// 注册新用户
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<UserDto>>> RegisterUser([FromBody] CreateUserRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<object>.Error("输入数据无效", GetModelStateErrors()));
            }

            var user = await _authService.RegisterUserAsync(request);
            return CreatedAtAction(nameof(GetUser), new { id = user.Id }, 
                ApiResponse<UserDto>.Success(user, "创建用户成功"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "注册用户业务逻辑错误");
            return BadRequest(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "注册用户失败");
            return StatusCode(500, ApiResponse<object>.Error("创建用户失败"));
        }
    }

    /// <summary>
    /// 根据ID获取用户
    /// </summary>
    [HttpGet("{id:int}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<UserDto>>> GetUser(int id)
    {
        try
        {
            var users = await _authService.GetAllUsersAsync();
            var user = users.FirstOrDefault(u => u.Id == id);
            
            if (user == null)
            {
                return NotFound(ApiResponse<object>.Error("用户不存在"));
            }

            return Ok(ApiResponse<UserDto>.Success(user, "获取用户信息成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户失败: UserId = {UserId}", id);
            return StatusCode(500, ApiResponse<object>.Error("获取用户信息失败"));
        }
    }

    /// <summary>
    /// 更新用户信息
    /// </summary>
    [HttpPut("{id:int}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<UserDto>>> UpdateUser(int id, [FromBody] UpdateUserRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<object>.Error("输入数据无效", GetModelStateErrors()));
            }

            var user = await _authService.UpdateUserAsync(id, request);
            return Ok(ApiResponse<UserDto>.Success(user, "更新用户成功"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "更新用户业务逻辑错误: UserId = {UserId}", id);
            return BadRequest(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户失败: UserId = {UserId}", id);
            return StatusCode(500, ApiResponse<object>.Error("更新用户失败"));
        }
    }

    /// <summary>
    /// 删除用户
    /// </summary>
    [HttpDelete("{id:int}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<object>>> DeleteUser(int id)
    {
        try
        {
            // 检查是否尝试删除当前用户
            var currentUserIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
            if (currentUserIdClaim != null && int.TryParse(currentUserIdClaim.Value, out var currentUserId))
            {
                if (currentUserId == id)
                {
                    return BadRequest(ApiResponse<object>.Error("不能删除当前登录用户"));
                }
            }

            await _authService.DeleteUserAsync(id);
            return Ok(ApiResponse<object>.Success(null, "删除用户成功"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "删除用户业务逻辑错误: UserId = {UserId}", id);
            return BadRequest(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除用户失败: UserId = {UserId}", id);
            return StatusCode(500, ApiResponse<object>.Error("删除用户失败"));
        }
    }

    /// <summary>
    /// 重置用户密码
    /// </summary>
    [HttpPost("{id:int}/reset-password")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<string>>> ResetUserPassword(int id, [FromBody] ResetPasswordRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<object>.Error("输入数据无效", GetModelStateErrors()));
            }

            // 创建密码更改请求
            var changePasswordRequest = new ChangePasswordRequest
            {
                OldPassword = "", // 管理员重置不需要验证旧密码
                NewPassword = request.NewPassword
            };

            // 由于这是管理员重置，需要特殊处理
            // 在实际应用中，应该有专门的重置密码方法
            _logger.LogWarning("管理员重置密码功能需要完善实现");
            
            return BadRequest(ApiResponse<object>.Error("重置密码功能暂未实现"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置用户密码失败: UserId = {UserId}", id);
            return StatusCode(500, ApiResponse<object>.Error("重置用户密码失败"));
        }
    }

    /// <summary>
    /// 启用/禁用用户
    /// </summary>
    [HttpPost("{id:int}/toggle-status")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<UserDto>>> ToggleUserStatus(int id)
    {
        try
        {
            // 检查是否尝试禁用当前用户
            var currentUserIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
            if (currentUserIdClaim != null && int.TryParse(currentUserIdClaim.Value, out var currentUserId))
            {
                if (currentUserId == id)
                {
                    return BadRequest(ApiResponse<object>.Error("不能禁用当前登录用户"));
                }
            }

            // 获取用户当前状态
            var users = await _authService.GetAllUsersAsync();
            var user = users.FirstOrDefault(u => u.Id == id);
            
            if (user == null)
            {
                return NotFound(ApiResponse<object>.Error("用户不存在"));
            }

            // 切换状态
            var updateRequest = new UpdateUserRequest
            {
                IsActive = !user.IsActive
            };

            var updatedUser = await _authService.UpdateUserAsync(id, updateRequest);
            var statusText = updatedUser.IsActive ? "启用" : "禁用";
            
            return Ok(ApiResponse<UserDto>.Success(updatedUser, $"{statusText}用户成功"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "切换用户状态业务逻辑错误: UserId = {UserId}", id);
            return BadRequest(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "切换用户状态失败: UserId = {UserId}", id);
            return StatusCode(500, ApiResponse<object>.Error("切换用户状态失败"));
        }
    }

    /// <summary>
    /// 获取用户统计信息
    /// </summary>
    [HttpGet("statistics")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<UserStatisticsDto>>> GetUserStatistics()
    {
        try
        {
            var users = await _authService.GetAllUsersAsync();
            var usersList = users.ToList();

            var statistics = new UserStatisticsDto
            {
                TotalUsers = usersList.Count,
                ActiveUsers = usersList.Count(u => u.IsActive),
                InactiveUsers = usersList.Count(u => !u.IsActive),
                AdminUsers = usersList.Count(u => u.Role == Shared.Enums.UserRole.Admin),
                OperatorUsers = usersList.Count(u => u.Role == Shared.Enums.UserRole.Operator),
                ViewerUsers = usersList.Count(u => u.Role == Shared.Enums.UserRole.Viewer),
                RecentLoginUsers = usersList.Count(u => u.LastLoginAt.HasValue && 
                    u.LastLoginAt.Value >= DateTime.UtcNow.AddDays(-7)),
                GeneratedAt = DateTime.UtcNow
            };

            return Ok(ApiResponse<UserStatisticsDto>.Success(statistics, "获取用户统计信息成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户统计信息失败");
            return StatusCode(500, ApiResponse<object>.Error("获取用户统计信息失败"));
        }
    }

    private Dictionary<string, string[]> GetModelStateErrors()
    {
        return ModelState
            .Where(x => x.Value?.Errors?.Count > 0)
            .ToDictionary(
                kvp => kvp.Key,
                kvp => kvp.Value?.Errors?.Select(e => e.ErrorMessage).ToArray() ?? Array.Empty<string>()
            );
    }
}

/// <summary>
/// 重置密码请求
/// </summary>
public class ResetPasswordRequest
{
    [Required(ErrorMessage = "新密码不能为空")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
    public string NewPassword { get; set; } = "";
}

/// <summary>
/// 用户统计信息
/// </summary>
public class UserStatisticsDto
{
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public int InactiveUsers { get; set; }
    public int AdminUsers { get; set; }
    public int OperatorUsers { get; set; }
    public int ViewerUsers { get; set; }
    public int RecentLoginUsers { get; set; }
    public DateTime GeneratedAt { get; set; }
}