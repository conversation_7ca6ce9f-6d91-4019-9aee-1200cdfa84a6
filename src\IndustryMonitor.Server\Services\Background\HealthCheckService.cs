using Microsoft.Extensions.Diagnostics.HealthChecks;
using IndustryMonitor.Server.Services.Device;
using IndustryMonitor.Server.Services.Cache;

namespace IndustryMonitor.Server.Services.Background;

/// <summary>
/// 系统健康检查后台服务
/// 定期检查系统各组件的健康状态
/// </summary>
public class HealthCheckService : BackgroundService
{
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly IConnectionPool _connectionPool;
    private readonly ICacheService _cacheService;
    private readonly ILogger<HealthCheckService> _logger;
    
    private readonly Dictionary<string, HealthStatus> _componentStatus = new();
    
    public HealthCheckService(
        IServiceScopeFactory scopeFactory,
        IConnectionPool connectionPool,
        ICacheService cacheService,
        ILogger<HealthCheckService> logger)
    {
        _scopeFactory = scopeFactory;
        _connectionPool = connectionPool;
        _cacheService = cacheService;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("系统健康检查后台服务已启动");
        
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await PerformHealthChecksAsync();
                
                // 每5分钟执行一次健康检查
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "系统健康检查过程中发生错误");
                
                // 发生错误时等待30秒后重试
                await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
            }
        }
        
        _logger.LogInformation("系统健康检查后台服务已停止");
    }

    private async Task PerformHealthChecksAsync()
    {
        var healthResults = new Dictionary<string, HealthCheckResult>();
        
        // 检查数据库连接
        healthResults["Database"] = await CheckDatabaseHealthAsync();
        
        // 检查Redis缓存
        healthResults["Cache"] = await CheckCacheHealthAsync();
        
        // 检查连接池状态
        healthResults["ConnectionPool"] = await CheckConnectionPoolHealthAsync();
        
        // 检查磁盘空间
        healthResults["DiskSpace"] = CheckDiskSpaceHealth();
        
        // 检查内存使用
        healthResults["Memory"] = CheckMemoryHealth();
        
        // 记录健康状态
        await LogHealthStatusAsync(healthResults);
        
        // 检查是否有组件不健康
        var unhealthyComponents = healthResults
            .Where(kv => kv.Value.Status != HealthStatus.Healthy)
            .ToList();
        
        if (unhealthyComponents.Any())
        {
            _logger.LogWarning("检测到 {Count} 个不健康的组件: {Components}", 
                unhealthyComponents.Count, 
                string.Join(", ", unhealthyComponents.Select(c => c.Key)));
        }
    }

    private async Task<HealthCheckResult> CheckDatabaseHealthAsync()
    {
        try
        {
            using var scope = _scopeFactory.CreateScope();
            var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
            
            var startTime = DateTime.UtcNow;
            await unitOfWork.Users.CountAsync();
            var responseTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
            
            _componentStatus["Database"] = HealthStatus.Healthy;
            
            return HealthCheckResult.Healthy($"数据库连接正常，响应时间: {responseTime:F2}ms");
        }
        catch (Exception ex)
        {
            _componentStatus["Database"] = HealthStatus.Unhealthy;
            return HealthCheckResult.Unhealthy("数据库连接失败", ex);
        }
    }

    private async Task<HealthCheckResult> CheckCacheHealthAsync()
    {
        try
        {
            var testKey = "health_check_" + Guid.NewGuid();
            var testValue = DateTime.UtcNow.ToString();
            
            var startTime = DateTime.UtcNow;
            await _cacheService.SetAsync(testKey, testValue, TimeSpan.FromSeconds(10));
            var cachedValue = await _cacheService.GetAsync<string>(testKey);
            await _cacheService.RemoveAsync(testKey);
            var responseTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
            
            if (cachedValue == testValue)
            {
                _componentStatus["Cache"] = HealthStatus.Healthy;
                return HealthCheckResult.Healthy($"缓存服务正常，响应时间: {responseTime:F2}ms");
            }
            else
            {
                _componentStatus["Cache"] = HealthStatus.Degraded;
                return HealthCheckResult.Degraded("缓存服务数据不一致");
            }
        }
        catch (Exception ex)
        {
            _componentStatus["Cache"] = HealthStatus.Unhealthy;
            return HealthCheckResult.Unhealthy("缓存服务连接失败", ex);
        }
    }

    private async Task<HealthCheckResult> CheckConnectionPoolHealthAsync()
    {
        try
        {
            var stats = await _connectionPool.GetStatisticsAsync();
            
            if (stats.TotalConnections == 0)
            {
                _componentStatus["ConnectionPool"] = HealthStatus.Healthy;
                return HealthCheckResult.Healthy("连接池正常（无活跃连接）");
            }
            
            var utilizationRate = (double)stats.ActiveConnections / stats.TotalConnections;
            
            if (utilizationRate > 0.9)
            {
                _componentStatus["ConnectionPool"] = HealthStatus.Degraded;
                return HealthCheckResult.Degraded(
                    $"连接池使用率较高: {utilizationRate:P2} ({stats.ActiveConnections}/{stats.TotalConnections})");
            }
            else
            {
                _componentStatus["ConnectionPool"] = HealthStatus.Healthy;
                return HealthCheckResult.Healthy(
                    $"连接池正常，使用率: {utilizationRate:P2} ({stats.ActiveConnections}/{stats.TotalConnections})");
            }
        }
        catch (Exception ex)
        {
            _componentStatus["ConnectionPool"] = HealthStatus.Unhealthy;
            return HealthCheckResult.Unhealthy("连接池状态检查失败", ex);
        }
    }

    private HealthCheckResult CheckDiskSpaceHealth()
    {
        try
        {
            var currentDirectory = Directory.GetCurrentDirectory();
            var driveInfo = new DriveInfo(Path.GetPathRoot(currentDirectory) ?? "C:");
            
            var freeSpaceGB = driveInfo.AvailableFreeSpace / (1024.0 * 1024.0 * 1024.0);
            var totalSpaceGB = driveInfo.TotalSize / (1024.0 * 1024.0 * 1024.0);
            var usagePercentage = (1.0 - (double)driveInfo.AvailableFreeSpace / driveInfo.TotalSize) * 100;
            
            if (freeSpaceGB < 1.0) // 少于1GB
            {
                _componentStatus["DiskSpace"] = HealthStatus.Unhealthy;
                return HealthCheckResult.Unhealthy(
                    $"磁盘空间严重不足: {freeSpaceGB:F2}GB 可用, 使用率: {usagePercentage:F1}%");
            }
            else if (usagePercentage > 90)
            {
                _componentStatus["DiskSpace"] = HealthStatus.Degraded;
                return HealthCheckResult.Degraded(
                    $"磁盘空间不足: {freeSpaceGB:F2}GB 可用, 使用率: {usagePercentage:F1}%");
            }
            else
            {
                _componentStatus["DiskSpace"] = HealthStatus.Healthy;
                return HealthCheckResult.Healthy(
                    $"磁盘空间充足: {freeSpaceGB:F2}GB 可用 / {totalSpaceGB:F2}GB 总计, 使用率: {usagePercentage:F1}%");
            }
        }
        catch (Exception ex)
        {
            _componentStatus["DiskSpace"] = HealthStatus.Unhealthy;
            return HealthCheckResult.Unhealthy("磁盘空间检查失败", ex);
        }
    }

    private HealthCheckResult CheckMemoryHealth()
    {
        try
        {
            var currentProcess = System.Diagnostics.Process.GetCurrentProcess();
            var workingSetMB = currentProcess.WorkingSet64 / (1024.0 * 1024.0);
            var privateMemoryMB = currentProcess.PrivateMemorySize64 / (1024.0 * 1024.0);
            
            // 获取系统总内存
            var gcMemoryInfo = GC.GetGCMemoryInfo();
            var totalMemoryMB = gcMemoryInfo.TotalAvailableMemoryBytes / (1024.0 * 1024.0);
            var memoryUsagePercentage = (workingSetMB / totalMemoryMB) * 100;
            
            if (workingSetMB > 2000) // 超过2GB
            {
                _componentStatus["Memory"] = HealthStatus.Degraded;
                return HealthCheckResult.Degraded(
                    $"内存使用量较高: 工作集 {workingSetMB:F2}MB, 私有内存 {privateMemoryMB:F2}MB, 系统使用率: {memoryUsagePercentage:F1}%");
            }
            else
            {
                _componentStatus["Memory"] = HealthStatus.Healthy;
                return HealthCheckResult.Healthy(
                    $"内存使用正常: 工作集 {workingSetMB:F2}MB, 私有内存 {privateMemoryMB:F2}MB, 系统使用率: {memoryUsagePercentage:F1}%");
            }
        }
        catch (Exception ex)
        {
            _componentStatus["Memory"] = HealthStatus.Unhealthy;
            return HealthCheckResult.Unhealthy("内存状态检查失败", ex);
        }
    }

    private async Task LogHealthStatusAsync(Dictionary<string, HealthCheckResult> healthResults)
    {
        try
        {
            var healthyCount = healthResults.Count(h => h.Value.Status == HealthStatus.Healthy);
            var degradedCount = healthResults.Count(h => h.Value.Status == HealthStatus.Degraded);
            var unhealthyCount = healthResults.Count(h => h.Value.Status == HealthStatus.Unhealthy);
            
            var overallStatus = unhealthyCount > 0 ? "不健康" : 
                              degradedCount > 0 ? "降级" : "健康";
            
            _logger.LogInformation(
                "系统健康检查完成 - 总体状态: {OverallStatus}, " +
                "健康: {Healthy}, 降级: {Degraded}, 不健康: {Unhealthy}",
                overallStatus, healthyCount, degradedCount, unhealthyCount);
            
            // 缓存健康状态信息
            var healthSummary = new
            {
                Timestamp = DateTime.UtcNow,
                OverallStatus = overallStatus,
                Components = healthResults.ToDictionary(
                    kv => kv.Key,
                    kv => new
                    {
                        Status = kv.Value.Status.ToString(),
                        Description = kv.Value.Description,
                        Exception = kv.Value.Exception?.Message
                    })
            };
            
            await _cacheService.SetAsync("system_health", healthSummary, TimeSpan.FromMinutes(10));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录健康状态信息失败");
        }
    }
}