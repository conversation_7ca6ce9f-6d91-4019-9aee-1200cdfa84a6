namespace IndustryMonitor.Server.Services.Cache;

/// <summary>
/// 缓存服务接口
/// </summary>
public interface ICacheService
{
    Task<T?> GetAsync<T>(string key) where T : class;
    Task<T> GetAsync<T>(string key, T defaultValue) where T : class;
    Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class;
    Task<bool> SetAsync<T>(string key, T value, TimeSpan expiration, bool onlyIfNotExists) where T : class;
    Task<bool> RemoveAsync(string key);
    Task<bool> ExistsAsync(string key);
    Task<long> RemoveByPatternAsync(string pattern);
    Task<IEnumerable<string>> GetKeysAsync(string pattern);
    Task<bool> SetStringAsync(string key, string value, TimeSpan? expiration = null);
    Task<string?> GetStringAsync(string key);
    Task<long> IncrementAsync(string key, long value = 1);
    Task<long> DecrementAsync(string key, long value = 1);
    Task<bool> ExpireAsync(string key, TimeSpan expiration);
    Task<TimeSpan?> GetTtlAsync(string key);
    Task FlushDatabaseAsync();
}

/// <summary>
/// 内存缓存服务接口
/// </summary>
public interface IMemoryCacheService
{
    T? Get<T>(string key) where T : class;
    void Set<T>(string key, T value, TimeSpan? expiration = null) where T : class;
    bool Remove(string key);
    bool Exists(string key);
    void Clear();
    int Count { get; }
}