namespace IndustryMonitor.Shared.Models.Configs;

/// <summary>
/// Modbus 通信配置
/// </summary>
public class ModbusConfig
{
    /// <summary>
    /// 最大读取连接数
    /// </summary>
    public int MaxReadConnections { get; set; } = 80;
    
    /// <summary>
    /// 最大写入连接数
    /// </summary>
    public int MaxWriteConnections { get; set; } = 20;
    
    /// <summary>
    /// 连接超时时间(毫秒)
    /// </summary>
    public int ConnectionTimeout { get; set; } = 3000;
    
    /// <summary>
    /// 读取超时时间(毫秒)
    /// </summary>
    public int ReadTimeout { get; set; } = 200;
    
    /// <summary>
    /// 写入超时时间(毫秒)
    /// </summary>
    public int WriteTimeout { get; set; } = 5000;
    
    /// <summary>
    /// 空闲连接超时时间(分钟)
    /// </summary>
    public int IdleTimeoutMinutes { get; set; } = 5;
    
    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 3;
    
    /// <summary>
    /// 重试延迟(毫秒)
    /// </summary>
    public int RetryDelayMs { get; set; } = 100;
}

/// <summary>
/// Redis 缓存配置
/// </summary>
public class RedisConfig
{
    /// <summary>
    /// 连接字符串
    /// </summary>
    public string ConnectionString { get; set; } = "localhost:6379";
    
    /// <summary>
    /// 数据库编号
    /// </summary>
    public int Database { get; set; } = 0;
    
    /// <summary>
    /// 键前缀
    /// </summary>
    public string KeyPrefix { get; set; } = "IndustryMonitor:";
    
    /// <summary>
    /// 默认过期时间
    /// </summary>
    public TimeSpan DefaultExpiry { get; set; } = TimeSpan.FromMinutes(5);
}

/// <summary>
/// JWT 配置
/// </summary>
public class JwtConfig
{
    /// <summary>
    /// 密钥
    /// </summary>
    public string Secret { get; set; } = "";
    
    /// <summary>
    /// 发行者
    /// </summary>
    public string Issuer { get; set; } = "IndustryMonitor.Server";
    
    /// <summary>
    /// 受众
    /// </summary>
    public string Audience { get; set; } = "IndustryMonitor.Client";
    
    /// <summary>
    /// Token 过期天数
    /// </summary>
    public int ExpiryDays { get; set; } = 7;
    
    /// <summary>
    /// 刷新 Token 过期天数
    /// </summary>
    public int RefreshTokenExpiryDays { get; set; } = 30;
}

/// <summary>
/// 系统配置
/// </summary>
public class SystemConfig
{
    /// <summary>
    /// 最大设备数量
    /// </summary>
    public int MaxDeviceCount { get; set; } = 200;
    
    /// <summary>
    /// 轮询间隔(毫秒)
    /// </summary>
    public int PollingIntervalMs { get; set; } = 500;
    
    /// <summary>
    /// 数据保留天数
    /// </summary>
    public int DataRetentionDays { get; set; } = 30;
    
    /// <summary>
    /// 最大并发写入任务数
    /// </summary>
    public int MaxConcurrentWriteTasks { get; set; } = 5;
    
    /// <summary>
    /// 启用健康检查
    /// </summary>
    public bool EnableHealthCheck { get; set; } = true;
    
    /// <summary>
    /// 启用 Swagger
    /// </summary>
    public bool EnableSwagger { get; set; } = true;
}