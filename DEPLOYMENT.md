# 工业设备监控系统 - 部署指南

本文档提供完整的系统部署步骤，包括服务端和客户端的详细安装配置说明。

## 📋 目录

1. [环境准备](#1-环境准备)
2. [服务端部署](#2-服务端部署)
3. [客户端部署](#3-客户端部署)
4. [数据库初始化](#4-数据库初始化)
5. [配置说明](#5-配置说明)
6. [启动服务](#6-启动服务)
7. [故障排查](#7-故障排查)
8. [维护指南](#8-维护指南)

---

## 1. 环境准备

### 1.1 硬件要求

#### 服务端主机
| 配置项 | 最低要求 | 推荐配置 |
|--------|----------|----------|
| CPU | 4核 2.0GHz | 8核 3.0GHz |
| 内存 | 8GB | 16GB |
| 存储 | 100GB SSD | 500GB SSD |
| 网络 | 千兆以太网 | 千兆以太网 |
| 操作系统 | Windows Server 2016 | Windows Server 2022 |

#### 客户端终端
| 配置项 | 最低要求 | 推荐配置 |
|--------|----------|----------|
| CPU | 双核 1.8GHz | 四核 2.5GHz |
| 内存 | 4GB | 8GB |
| 存储 | 20GB | 50GB |
| 显示器 | 1366×768 | 1920×1080 |
| 操作系统 | Windows 10 | Windows 11 |

### 1.2 网络拓扑

```
     ┌─────────────────────────────────────────────┐
     │              工业网络环境                    │
     │                                             │
     │  ┌──────────┐ ┌──────────┐ ┌──────────┐     │
     │  │ PLC设备1  │ │ PLC设备2  │ │ PLC设备n  │     │
     │  │192.168.1.│ │192.168.1.│ │192.168.1.│     │
     │  │    10    │ │    11    │ │   209    │     │
     │  └──────────┘ └──────────┘ └──────────┘     │
     │              │                              │
     └──────────────┼──────────────────────────────┘
                    │
      ┌─────────────┼─────────────────────────────┐
      │             │    管理网络                  │
      │             │                             │
      │    ┌────────▼────────┐                    │
      │    │   服务端主机     │                    │
      │    │ *************   │                    │
      │    └─────────────────┘                    │
      │             │                             │
      │  ┌──────────┼──────────────┐              │
      │  │          │              │              │
      │ ┌▼──────┐ ┌─▼──────┐ ┌────▼──┐ ┌────────┐ │
      │ │客户端1│ │客户端2│ │客户端3│ │客户端4│  │
      │ │ .101  │ │ .102  │ │ .103  │ │ .104  │  │
      │ └───────┘ └───────┘ └───────┘ └────────┘  │
      └─────────────────────────────────────────────┘
```

### 1.3 软件依赖

#### 服务端依赖
- [.NET 8.0 Runtime](https://dotnet.microsoft.com/download/dotnet/8.0)
- [Redis Server 6.0+](https://redis.io/download)
- [Visual C++ Redistributable](https://aka.ms/vs/17/release/vc_redist.x64.exe)

#### 客户端依赖
- [.NET 8.0 Desktop Runtime](https://dotnet.microsoft.com/download/dotnet/8.0)

---

## 2. 服务端部署

### 2.1 安装系统依赖

#### 安装 .NET 8.0 Runtime
```powershell
# 下载并安装 .NET 8.0 Runtime
Invoke-WebRequest -Uri "https://download.visualstudio.microsoft.com/download/pr/xxx/dotnet-runtime-8.0-win-x64.exe" -OutFile "dotnet-runtime-8.0.exe"
.\dotnet-runtime-8.0.exe /quiet
```

#### 安装 Redis Server
```powershell
# 使用 Chocolatey 安装 Redis
Set-ExecutionPolicy Bypass -Scope Process -Force
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

choco install redis-64 -y

# 启动 Redis 服务
redis-server --service-install
redis-server --service-start
```

### 2.2 创建部署目录

```powershell
# 创建应用程序目录
New-Item -Path "C:\IndustryMonitor" -ItemType Directory -Force
New-Item -Path "C:\IndustryMonitor\Server" -ItemType Directory -Force
New-Item -Path "C:\IndustryMonitor\Logs" -ItemType Directory -Force
New-Item -Path "C:\IndustryMonitor\Database" -ItemType Directory -Force
New-Item -Path "C:\IndustryMonitor\Backup" -ItemType Directory -Force

# 设置目录权限
$acl = Get-Acl "C:\IndustryMonitor"
$accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS","FullControl","ContainerInherit,ObjectInherit","None","Allow")
$acl.SetAccessRule($accessRule)
Set-Acl "C:\IndustryMonitor" $acl
```

### 2.3 部署应用程序

#### 发布服务端程序
```bash
# 在开发机上发布程序
cd IndustryMonitor.Server
dotnet publish -c Release -r win-x64 --self-contained false -o publish

# 或者创建部署包
dotnet publish -c Release -r win-x64 --self-contained false -o publish
Compress-Archive -Path publish\* -DestinationPath IndustryMonitor.Server.zip
```

#### 复制文件到服务器
```powershell
# 解压部署包到目标目录
Expand-Archive -Path "IndustryMonitor.Server.zip" -DestinationPath "C:\IndustryMonitor\Server" -Force

# 验证文件完整性
Get-ChildItem "C:\IndustryMonitor\Server" -Recurse | Measure-Object -Property Length -Sum
```

### 2.4 配置应用程序

#### 创建生产环境配置文件
```json
# C:\IndustryMonitor\Server\appsettings.Production.json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "Microsoft.EntityFrameworkCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://0.0.0.0:5000"
      }
    },
    "Limits": {
      "MaxConcurrentConnections": 100,
      "MaxRequestBodySize": 10485760
    }
  },
  "ConnectionStrings": {
    "SQLite": "Data Source=C:\\IndustryMonitor\\Database\\industry.db;Cache=Shared;",
    "Redis": "localhost:6379,password=YourRedisPassword123,database=0,connectTimeout=5000,syncTimeout=1000"
  },
  "ModbusSettings": {
    "MaxReadConnections": 80,
    "MaxWriteConnections": 20,
    "ConnectionTimeout": 3000,
    "ReadTimeout": 200,
    "WriteTimeout": 5000,
    "IdleTimeoutMinutes": 5,
    "RetryCount": 3,
    "RetryDelayMs": 100
  },
  "RedisSettings": {
    "ConnectionString": "localhost:6379",
    "Database": 0,
    "KeyPrefix": "IndustryMonitor:",
    "DefaultExpiry": "00:05:00"
  },
  "JwtSettings": {
    "Secret": "YourJwtSecretKeyMustBeAtLeast32CharactersLong!",
    "Issuer": "IndustryMonitor.Server",
    "Audience": "IndustryMonitor.Client",
    "ExpiryDays": 7,
    "RefreshTokenExpiryDays": 30
  },
  "SystemSettings": {
    "MaxDeviceCount": 200,
    "PollingIntervalMs": 500,
    "DataRetentionDays": 30,
    "MaxConcurrentWriteTasks": 5,
    "EnableHealthCheck": true,
    "EnableSwagger": false
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "C:\\IndustryMonitor\\Logs\\app-.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30,
          "fileSizeLimitBytes": 104857600,
          "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] {Message:lj}{NewLine}{Exception}"
        }
      },
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "{Timestamp:HH:mm:ss} [{Level:u3}] {Message:lj}{NewLine}{Exception}"
        }
      }
    ]
  }
}
```

#### 设置 Redis 密码
```powershell
# 编辑 Redis 配置文件
notepad "C:\Program Files\Redis\redis.windows-service.conf"

# 添加以下配置
# requirepass YourRedisPassword123
# maxmemory 1gb
# maxmemory-policy allkeys-lru

# 重启 Redis 服务
Restart-Service Redis
```

### 2.5 安装为 Windows 服务

#### 创建服务安装脚本
```powershell
# install-service.ps1
param(
    [string]$ServiceName = "IndustryMonitorServer",
    [string]$DisplayName = "工业设备监控系统服务端",
    [string]$Description = "工业设备监控与配方下发系统服务端",
    [string]$BinaryPath = "C:\IndustryMonitor\Server\IndustryMonitor.Server.exe"
)

# 检查是否以管理员身份运行
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator"))
{
    Write-Host "请以管理员身份运行此脚本" -ForegroundColor Red
    exit 1
}

try {
    # 停止并删除已存在的服务
    $existingService = Get-Service $ServiceName -ErrorAction SilentlyContinue
    if ($existingService) {
        Write-Host "停止现有服务..." -ForegroundColor Yellow
        Stop-Service $ServiceName -Force
        sc.exe delete $ServiceName
        Start-Sleep -Seconds 2
    }
    
    # 创建新服务
    Write-Host "创建服务 $ServiceName..." -ForegroundColor Green
    sc.exe create $ServiceName binPath= "`"$BinaryPath`"" start= auto displayname= "`"$DisplayName`""
    sc.exe description $ServiceName "`"$Description`""
    
    # 配置服务恢复选项
    sc.exe failure $ServiceName reset= 86400 actions= restart/5000/restart/5000/restart/5000
    
    # 启动服务
    Write-Host "启动服务..." -ForegroundColor Green
    Start-Service $ServiceName
    
    # 检查服务状态
    $service = Get-Service $ServiceName
    if ($service.Status -eq "Running") {
        Write-Host "服务安装并启动成功!" -ForegroundColor Green
        Write-Host "服务名称: $($service.Name)"
        Write-Host "显示名称: $($service.DisplayName)"
        Write-Host "运行状态: $($service.Status)"
    } else {
        Write-Host "服务安装成功但启动失败，状态: $($service.Status)" -ForegroundColor Red
    }
}
catch {
    Write-Host "服务安装失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
```

#### 执行服务安装
```powershell
# 以管理员身份执行
.\install-service.ps1

# 验证服务状态
Get-Service IndustryMonitorServer
```

### 2.6 配置防火墙

```powershell
# 添加防火墙规则
New-NetFirewallRule -DisplayName "IndustryMonitor HTTP" -Direction Inbound -Protocol TCP -LocalPort 5000 -Action Allow
New-NetFirewallRule -DisplayName "IndustryMonitor SignalR" -Direction Inbound -Protocol TCP -LocalPort 5000 -Action Allow

# 如果需要外网访问，还需添加出站规则
New-NetFirewallRule -DisplayName "IndustryMonitor Outbound" -Direction Outbound -Protocol TCP -LocalPort 5000 -Action Allow

# 验证防火墙规则
Get-NetFirewallRule -DisplayName "*IndustryMonitor*"
```

---

## 3. 客户端部署

### 3.1 创建安装包

#### 使用 Inno Setup 创建安装程序

创建 `setup-script.iss` 文件：
```ini
[Setup]
AppName=工业设备监控系统
AppVersion=1.0.0
AppPublisher=您的公司名称
AppPublisherURL=https://www.yourcompany.com
AppSupportURL=https://www.yourcompany.com/support
DefaultDirName={autopf}\IndustryMonitor
DefaultGroupName=工业设备监控系统
AllowNoIcons=yes
LicenseFile=LICENSE.txt
InfoAfterFile=README.txt
OutputDir=.\Output
OutputBaseFilename=IndustryMonitorClientSetup
SetupIconFile=app.ico
Compression=lzma2
SolidCompression=yes
WizardStyle=modern
DisableProgramGroupPage=yes
PrivilegesRequired=admin

[Languages]
Name: "chinesesimp"; MessagesFile: "compiler:Languages\ChineseSimplified.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode

[Files]
Source: "C:\IndustryMonitor\Client\publish\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "appsettings.json"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\工业设备监控系统"; Filename: "{app}\IndustryMonitor.Client.exe"
Name: "{group}\{cm:UninstallProgram,工业设备监控系统}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\工业设备监控系统"; Filename: "{app}\IndustryMonitor.Client.exe"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\工业设备监控系统"; Filename: "{app}\IndustryMonitor.Client.exe"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\IndustryMonitor.Client.exe"; Description: "{cm:LaunchProgram,工业设备监控系统}"; Flags: nowait postinstall skipifsilent

[Code]
var
  ServerUrlPage: TInputQueryWizardPage;

procedure InitializeWizard;
begin
  ServerUrlPage := CreateInputQueryPage(wpSelectDir,
    '服务器配置', '请配置服务器连接信息',
    '请输入监控系统服务器的IP地址。如果服务器地址发生变化，您可以稍后在配置文件中修改。');
  ServerUrlPage.Add('服务器地址:', False);
  ServerUrlPage.Values[0] := '*************';
end;

procedure CurStepChanged(CurStep: TSetupStep);
var
  ConfigFile: string;
  Config: TStringList;
begin
  if CurStep = ssPostInstall then
  begin
    ConfigFile := ExpandConstant('{app}\appsettings.json');
    Config := TStringList.Create;
    try
      Config.Add('{');
      Config.Add('  "ServerUrl": "http://' + ServerUrlPage.Values[0] + ':5000",');
      Config.Add('  "SignalRHub": "http://' + ServerUrlPage.Values[0] + ':5000/deviceHub",');
      Config.Add('  "AutoReconnect": true,');
      Config.Add('  "ReconnectInterval": 5000,');
      Config.Add('  "Theme": "Light",');
      Config.Add('  "Language": "zh-CN",');
      Config.Add('  "LogLevel": "Information"');
      Config.Add('}');
      Config.SaveToFile(ConfigFile);
    finally
      Config.Free;
    end;
  end;
end;
```

#### 编译安装包
```powershell
# 安装 Inno Setup
# 下载: https://jrsoftware.org/isdl.php

# 编译安装包
"C:\Program Files (x86)\Inno Setup 6\ISCC.exe" setup-script.iss

# 生成的安装包位于 .\Output\IndustryMonitorClientSetup.exe
```

### 3.2 批量部署脚本

#### 创建静默安装脚本
```powershell
# silent-install.ps1
param(
    [string]$ServerIP = "*************",
    [string]$InstallerPath = ".\IndustryMonitorClientSetup.exe"
)

Write-Host "开始静默安装工业设备监控系统客户端..." -ForegroundColor Green
Write-Host "服务器地址: $ServerIP" -ForegroundColor Yellow

try {
    # 检查安装包是否存在
    if (-not (Test-Path $InstallerPath)) {
        throw "安装包不存在: $InstallerPath"
    }
    
    # 执行静默安装
    $process = Start-Process -FilePath $InstallerPath -ArgumentList "/VERYSILENT", "/NORESTART" -Wait -PassThru
    
    if ($process.ExitCode -eq 0) {
        Write-Host "客户端安装成功!" -ForegroundColor Green
        
        # 更新配置文件
        $configPath = "$env:ProgramFiles\IndustryMonitor\appsettings.json"
        if (Test-Path $configPath) {
            $config = @{
                ServerUrl = "http://$ServerIP:5000"
                SignalRHub = "http://$ServerIP:5000/deviceHub"
                AutoReconnect = $true
                ReconnectInterval = 5000
                Theme = "Light"
                Language = "zh-CN"
                LogLevel = "Information"
            }
            
            $config | ConvertTo-Json -Depth 10 | Set-Content $configPath -Encoding UTF8
            Write-Host "配置文件已更新" -ForegroundColor Green
        }
        
        # 创建桌面快捷方式
        $WshShell = New-Object -comObject WScript.Shell
        $Shortcut = $WshShell.CreateShortcut("$env:USERPROFILE\Desktop\工业设备监控系统.lnk")
        $Shortcut.TargetPath = "$env:ProgramFiles\IndustryMonitor\IndustryMonitor.Client.exe"
        $Shortcut.WorkingDirectory = "$env:ProgramFiles\IndustryMonitor"
        $Shortcut.IconLocation = "$env:ProgramFiles\IndustryMonitor\IndustryMonitor.Client.exe,0"
        $Shortcut.Save()
        
        Write-Host "部署完成! 桌面快捷方式已创建" -ForegroundColor Green
    }
    else {
        throw "安装失败，退出代码: $($process.ExitCode)"
    }
}
catch {
    Write-Host "安装失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
```

### 3.3 网络部署

#### 使用组策略部署
1. 将安装包放到共享目录
2. 创建组策略对象 (GPO)
3. 配置软件安装策略
4. 应用到目标计算机组织单位

#### 使用 PowerShell DSC 部署
```powershell
# ClientDeployment.ps1 - PowerShell DSC配置
Configuration IndustryMonitorClientDeployment
{
    param(
        [string[]]$ComputerName,
        [string]$ServerIP,
        [string]$InstallerPath
    )
    
    Node $ComputerName
    {
        # 确保安装目录存在
        File InstallDirectory
        {
            Type = 'Directory'
            DestinationPath = 'C:\Program Files\IndustryMonitor'
            Ensure = 'Present'
        }
        
        # 安装应用程序
        Package IndustryMonitorClient
        {
            Ensure = 'Present'
            Name = '工业设备监控系统'
            Path = $InstallerPath
            ProductId = '' # 从安装包获取
            Arguments = "/VERYSILENT /NORESTART"
            DependsOn = '[File]InstallDirectory'
        }
        
        # 配置文件
        File ClientConfig
        {
            Type = 'File'
            DestinationPath = 'C:\Program Files\IndustryMonitor\appsettings.json'
            Contents = @"
{
  "ServerUrl": "http://$ServerIP:5000",
  "SignalRHub": "http://$ServerIP:5000/deviceHub",
  "AutoReconnect": true,
  "ReconnectInterval": 5000,
  "Theme": "Light",
  "Language": "zh-CN",
  "LogLevel": "Information"
}
"@
            DependsOn = '[Package]IndustryMonitorClient'
        }
    }
}

# 编译配置
IndustryMonitorClientDeployment -ComputerName @("Terminal1", "Terminal2", "Terminal3", "Terminal4") -ServerIP "*************" -InstallerPath "\\Server\Share\IndustryMonitorClientSetup.exe"

# 应用配置
Start-DscConfiguration -Path .\IndustryMonitorClientDeployment -ComputerName Terminal1,Terminal2,Terminal3,Terminal4 -Verbose
```

---

## 4. 数据库初始化

### 4.1 自动初始化

应用程序首次启动时会自动创建数据库和表结构：

```csharp
// 在 Program.cs 中
public static async Task Main(string[] args)
{
    var host = CreateHostBuilder(args).Build();
    
    // 数据库初始化
    using (var scope = host.Services.CreateScope())
    {
        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        await context.Database.EnsureCreatedAsync();
        
        // 运行数据库迁移
        await context.Database.MigrateAsync();
        
        // 初始化种子数据
        await SeedDataAsync(context);
    }
    
    await host.RunAsync();
}
```

### 4.2 手动初始化脚本

```sql
-- 创建数据库初始化脚本 init-database.sql

-- 设备信息表
CREATE TABLE IF NOT EXISTS "Devices" (
    "Id" INTEGER PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "IpAddress" TEXT NOT NULL UNIQUE,
    "Port" INTEGER DEFAULT 502,
    "SlaveId" INTEGER DEFAULT 1,
    "RegisterStart" INTEGER NOT NULL,
    "RegisterCount" INTEGER NOT NULL,
    "DeviceType" TEXT,
    "Location" TEXT,
    "Description" TEXT,
    "IsActive" INTEGER DEFAULT 1,
    "CreatedAt" TEXT DEFAULT (datetime('now')),
    "UpdatedAt" TEXT DEFAULT (datetime('now'))
);

-- 配方管理表
CREATE TABLE IF NOT EXISTS "Recipes" (
    "Id" INTEGER PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "Description" TEXT,
    "Content" TEXT NOT NULL,
    "Version" TEXT DEFAULT '1.0',
    "IsLocked" INTEGER DEFAULT 0,
    "LockedBy" TEXT,
    "LockedAt" TEXT,
    "CreatedBy" TEXT,
    "CreatedAt" TEXT DEFAULT (datetime('now')),
    "UpdatedBy" TEXT,
    "UpdatedAt" TEXT DEFAULT (datetime('now'))
);

-- 用户表
CREATE TABLE IF NOT EXISTS "Users" (
    "Id" INTEGER PRIMARY KEY AUTOINCREMENT,
    "Username" TEXT NOT NULL UNIQUE,
    "PasswordHash" TEXT NOT NULL,
    "FullName" TEXT,
    "Role" TEXT DEFAULT 'Operator',
    "IsActive" INTEGER DEFAULT 1,
    "LastLoginAt" TEXT,
    "CreatedAt" TEXT DEFAULT (datetime('now')),
    "UpdatedAt" TEXT DEFAULT (datetime('now'))
);

-- 操作日志表
CREATE TABLE IF NOT EXISTS "OperationLogs" (
    "Id" INTEGER PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER,
    "UserName" TEXT,
    "Operation" TEXT NOT NULL,
    "Target" TEXT,
    "Details" TEXT,
    "IpAddress" TEXT,
    "Result" TEXT DEFAULT 'Success',
    "CreatedAt" TEXT DEFAULT (datetime('now')),
    FOREIGN KEY ("UserId") REFERENCES "Users"("Id")
);

-- 写入任务表
CREATE TABLE IF NOT EXISTS "WriteTasks" (
    "Id" INTEGER PRIMARY KEY AUTOINCREMENT,
    "TaskId" TEXT NOT NULL UNIQUE,
    "RecipeId" INTEGER NOT NULL,
    "DeviceIds" TEXT NOT NULL,
    "Status" TEXT DEFAULT 'Pending',
    "Progress" REAL DEFAULT 0,
    "StartedAt" TEXT,
    "CompletedAt" TEXT,
    "Error" TEXT,
    "CreatedBy" TEXT,
    "CreatedAt" TEXT DEFAULT (datetime('now')),
    FOREIGN KEY ("RecipeId") REFERENCES "Recipes"("Id")
);

-- 设备历史数据表
CREATE TABLE IF NOT EXISTS "DeviceHistories" (
    "Id" INTEGER PRIMARY KEY AUTOINCREMENT,
    "DeviceId" INTEGER NOT NULL,
    "Data" TEXT NOT NULL,
    "Quality" TEXT DEFAULT 'Good',
    "Timestamp" TEXT DEFAULT (datetime('now')),
    FOREIGN KEY ("DeviceId") REFERENCES "Devices"("Id")
);

-- 创建索引
CREATE INDEX IF NOT EXISTS "IX_Devices_IpAddress" ON "Devices"("IpAddress");
CREATE INDEX IF NOT EXISTS "IX_Devices_IsActive" ON "Devices"("IsActive");
CREATE INDEX IF NOT EXISTS "IX_Recipes_IsLocked" ON "Recipes"("IsLocked");
CREATE INDEX IF NOT EXISTS "IX_WriteTasks_Status" ON "WriteTasks"("Status");
CREATE INDEX IF NOT EXISTS "IX_WriteTasks_CreatedAt" ON "WriteTasks"("CreatedAt");
CREATE INDEX IF NOT EXISTS "IX_OperationLogs_CreatedAt" ON "OperationLogs"("CreatedAt");
CREATE INDEX IF NOT EXISTS "IX_DeviceHistories_DeviceId" ON "DeviceHistories"("DeviceId");
CREATE INDEX IF NOT EXISTS "IX_DeviceHistories_Timestamp" ON "DeviceHistories"("Timestamp");

-- 插入默认管理员用户
INSERT OR IGNORE INTO "Users" ("Username", "PasswordHash", "FullName", "Role") 
VALUES ('admin', 'AQAAAAEAACcQAAAAELwYBx8TKzRTBwHCQoGzKzRTBwHCQoGzKzRTBwHC', '系统管理员', 'Admin');

-- 插入示例设备数据
INSERT OR IGNORE INTO "Devices" ("Name", "IpAddress", "RegisterStart", "RegisterCount", "DeviceType", "Location") VALUES
('1号注塑机', '************', 400001, 10, 'PLC', '车间A-01'),
('2号注塑机', '************', 400001, 10, 'PLC', '车间A-02'),
('3号注塑机', '************', 400001, 10, 'PLC', '车间A-03'),
('4号注塑机', '************', 400001, 10, 'PLC', '车间A-04'),
('5号注塑机', '************', 400001, 10, 'PLC', '车间A-05');

-- 插入示例配方数据
INSERT OR IGNORE INTO "Recipes" ("Name", "Description", "Content", "Version", "CreatedBy") VALUES
('产品A标准配方', '产品A的标准生产配方', '{"registers":[{"address":400001,"value":100},{"address":400002,"value":200}]}', '1.0', 'admin'),
('产品B标准配方', '产品B的标准生产配方', '{"registers":[{"address":400001,"value":150},{"address":400002,"value":250}]}', '1.0', 'admin');
```

### 4.3 执行初始化

```powershell
# 使用 SQLite 命令行工具执行初始化
sqlite3 "C:\IndustryMonitor\Database\industry.db" ".read init-database.sql"

# 或者使用 PowerShell
$connectionString = "Data Source=C:\IndustryMonitor\Database\industry.db"
$sql = Get-Content "init-database.sql" -Raw

Add-Type -AssemblyName System.Data.SQLite
$connection = New-Object System.Data.SQLite.SQLiteConnection($connectionString)
$connection.Open()

$command = $connection.CreateCommand()
$command.CommandText = $sql
$command.ExecuteNonQuery()

$connection.Close()
```

---

## 5. 配置说明

### 5.1 服务端配置详解

#### 核心配置项说明
```json
{
  // Kestrel Web服务器配置
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://0.0.0.0:5000"  // 监听所有网络接口的5000端口
      }
    },
    "Limits": {
      "MaxConcurrentConnections": 100,  // 最大并发连接数
      "MaxRequestBodySize": 10485760    // 最大请求体大小 (10MB)
    }
  },
  
  // 数据库连接配置
  "ConnectionStrings": {
    "SQLite": "Data Source=C:\\IndustryMonitor\\Database\\industry.db;Cache=Shared;",
    "Redis": "localhost:6379,password=YourPassword,database=0,connectTimeout=5000"
  },
  
  // Modbus通信配置
  "ModbusSettings": {
    "MaxReadConnections": 80,      // 最大读取连接数
    "MaxWriteConnections": 20,     // 最大写入连接数
    "ConnectionTimeout": 3000,     // 连接超时时间(ms)
    "ReadTimeout": 200,            // 读取超时时间(ms)
    "WriteTimeout": 5000,          // 写入超时时间(ms)
    "IdleTimeoutMinutes": 5,       // 空闲连接超时时间(分钟)
    "RetryCount": 3,               // 重试次数
    "RetryDelayMs": 100            // 重试延迟(ms)
  },
  
  // JWT认证配置
  "JwtSettings": {
    "Secret": "YourSecretKeyMustBeAtLeast32Characters!",  // 密钥(至少32字符)
    "Issuer": "IndustryMonitor.Server",                   // 颁发者
    "Audience": "IndustryMonitor.Client",                 // 受众
    "ExpiryDays": 7,                                      // Token过期天数
    "RefreshTokenExpiryDays": 30                          // 刷新Token过期天数
  },
  
  // 系统设置
  "SystemSettings": {
    "MaxDeviceCount": 200,          // 最大设备数量
    "PollingIntervalMs": 500,       // 轮询间隔(ms)
    "DataRetentionDays": 30,        // 数据保留天数
    "MaxConcurrentWriteTasks": 5,   // 最大并发写入任务数
    "EnableHealthCheck": true,      // 启用健康检查
    "EnableSwagger": false          // 启用Swagger文档(生产环境建议关闭)
  }
}
```

### 5.2 客户端配置详解

#### 客户端配置文件
```json
{
  // 服务器连接配置
  "ServerUrl": "http://*************:5000",              // 服务器地址
  "SignalRHub": "http://*************:5000/deviceHub",   // SignalR Hub地址
  
  // 连接设置
  "AutoReconnect": true,           // 自动重连
  "ReconnectInterval": 5000,       // 重连间隔(ms)
  "ConnectionTimeout": 30000,      // 连接超时时间(ms)
  "RequestTimeout": 10000,         // 请求超时时间(ms)
  
  // 界面设置
  "Theme": "Light",                // 主题: Light/Dark
  "Language": "zh-CN",             // 语言: zh-CN/en-US
  "AutoSave": true,                // 自动保存设置
  "RefreshInterval": 1000,         // 界面刷新间隔(ms)
  
  // 功能设置
  "EnableNotifications": true,     // 启用通知
  "EnableSounds": false,           // 启用声音提示
  "LogLevel": "Information",       // 日志级别
  "MaxLogFiles": 10,               // 最大日志文件数
  
  // 界面布局
  "WindowState": "Normal",         // 窗口状态
  "WindowWidth": 1200,             // 窗口宽度
  "WindowHeight": 800,             // 窗口高度
  "DeviceViewMode": "Grid"         // 设备显示模式: Grid/List
}
```

---

## 6. 启动服务

### 6.1 服务端启动

#### 检查服务状态
```powershell
# 检查Windows服务状态
Get-Service IndustryMonitorServer

# 检查端口监听状态
netstat -an | findstr :5000

# 检查Redis服务状态
Get-Service Redis
```

#### 启动服务
```powershell
# 启动Windows服务
Start-Service IndustryMonitorServer

# 或者直接运行程序(调试模式)
cd "C:\IndustryMonitor\Server"
.\IndustryMonitor.Server.exe

# 设置环境变量为生产模式
$env:ASPNETCORE_ENVIRONMENT = "Production"
.\IndustryMonitor.Server.exe
```

#### 验证服务运行
```powershell
# 测试HTTP接口
Invoke-RestMethod -Uri "http://localhost:5000/api/health" -Method GET

# 查看服务日志
Get-Content "C:\IndustryMonitor\Logs\app-*.log" -Tail 50
```

### 6.2 客户端启动

#### 手动启动
```powershell
# 直接运行客户端
& "C:\Program Files\IndustryMonitor\IndustryMonitor.Client.exe"

# 或从桌面快捷方式启动
Start-Process "$env:USERPROFILE\Desktop\工业设备监控系统.lnk"
```

#### 自动启动设置
```powershell
# 添加到启动项
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("$env:APPDATA\Microsoft\Windows\Start Menu\Programs\Startup\工业设备监控系统.lnk")
$Shortcut.TargetPath = "C:\Program Files\IndustryMonitor\IndustryMonitor.Client.exe"
$Shortcut.WorkingDirectory = "C:\Program Files\IndustryMonitor"
$Shortcut.WindowStyle = 1
$Shortcut.Save()
```

---

## 7. 故障排查

### 7.1 常见问题

#### 问题1: 服务无法启动
**症状**: Windows服务启动失败或立即停止

**排查步骤**:
```powershell
# 1. 检查事件日志
Get-WinEvent -LogName Application | Where-Object {$_.ProviderName -eq "IndustryMonitor.Server"} | Select-Object TimeCreated, LevelDisplayName, Message

# 2. 检查应用程序日志
Get-Content "C:\IndustryMonitor\Logs\app-*.log" | Select-String "Error|Fatal"

# 3. 验证配置文件
Test-Path "C:\IndustryMonitor\Server\appsettings.Production.json"

# 4. 检查端口占用
netstat -ano | findstr :5000
```

**解决方案**:
- 检查配置文件语法是否正确
- 确认端口5000未被其他程序占用
- 验证数据库文件权限
- 检查Redis服务是否正常运行

#### 问题2: 客户端无法连接服务器
**症状**: 客户端显示"无法连接到服务器"

**排查步骤**:
```powershell
# 1. 测试网络连通性
Test-NetConnection -ComputerName ************* -Port 5000

# 2. 检查防火墙设置
Get-NetFirewallRule -DisplayName "*IndustryMonitor*"

# 3. 验证服务端运行状态
Invoke-RestMethod -Uri "http://*************:5000/api/health"

# 4. 检查客户端配置
Get-Content "C:\Program Files\IndustryMonitor\appsettings.json"
```

**解决方案**:
- 确认服务端IP地址配置正确
- 检查网络连通性和防火墙设置
- 验证服务端服务运行状态
- 确认客户端配置文件中的服务器地址正确

#### 问题3: 设备连接失败
**症状**: 设备显示离线状态或连接错误

**排查步骤**:
```powershell
# 1. 检查设备网络连通性
Test-NetConnection -ComputerName ************ -Port 502

# 2. 使用Modbus测试工具验证设备通信
# 推荐工具: ModbusPoll 或 QModMaster

# 3. 检查设备配置
# 验证IP地址、端口、从站地址、寄存器地址等参数

# 4. 查看通信日志
Get-Content "C:\IndustryMonitor\Logs\app-*.log" | Select-String "Modbus|Device"
```

**解决方案**:
- 验证设备网络配置和连通性
- 确认Modbus参数设置正确
- 检查设备是否支持多个并发连接
- 调整连接池大小和超时设置

### 7.2 日志分析

#### 启用详细日志
```json
// 在 appsettings.Production.json 中临时启用调试日志
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "IndustryMonitor": "Trace",
      "Microsoft": "Warning"
    }
  }
}
```

#### 日志查看脚本
```powershell
# log-viewer.ps1 - 日志查看工具
param(
    [string]$LogPath = "C:\IndustryMonitor\Logs",
    [string]$Level = "Error",
    [int]$Lines = 100
)

Write-Host "查看系统日志 - 级别: $Level, 行数: $Lines" -ForegroundColor Green

# 获取最新的日志文件
$latestLog = Get-ChildItem $LogPath -Filter "app-*.log" | Sort-Object LastWriteTime -Descending | Select-Object -First 1

if ($latestLog) {
    Write-Host "日志文件: $($latestLog.FullName)" -ForegroundColor Yellow
    Write-Host "文件大小: $([math]::Round($latestLog.Length/1MB, 2)) MB" -ForegroundColor Yellow
    Write-Host "最后修改: $($latestLog.LastWriteTime)" -ForegroundColor Yellow
    Write-Host ""
    
    # 根据级别过滤日志
    $content = Get-Content $latestLog.FullName -Tail $Lines
    
    switch ($Level.ToUpper()) {
        "ERROR" { $filteredContent = $content | Select-String "\[ERR\]|\[Fatal\]" }
        "WARNING" { $filteredContent = $content | Select-String "\[WRN\]|\[ERR\]|\[Fatal\]" }
        "INFO" { $filteredContent = $content | Select-String "\[INF\]|\[WRN\]|\[ERR\]|\[Fatal\]" }
        "DEBUG" { $filteredContent = $content | Select-String "\[DBG\]|\[INF\]|\[WRN\]|\[ERR\]|\[Fatal\]" }
        default { $filteredContent = $content }
    }
    
    $filteredContent | ForEach-Object {
        $line = $_.Line
        if ($line -match "\[ERR\]|\[Fatal\]") {
            Write-Host $line -ForegroundColor Red
        }
        elseif ($line -match "\[WRN\]") {
            Write-Host $line -ForegroundColor Yellow
        }
        elseif ($line -match "\[INF\]") {
            Write-Host $line -ForegroundColor Green
        }
        else {
            Write-Host $line
        }
    }
}
else {
    Write-Host "未找到日志文件" -ForegroundColor Red
}
```

### 7.3 性能监控

#### 系统资源监控脚本
```powershell
# monitor-system.ps1 - 系统资源监控
param(
    [int]$IntervalSeconds = 60,
    [int]$DurationMinutes = 60
)

$endTime = (Get-Date).AddMinutes($DurationMinutes)
$logFile = "C:\IndustryMonitor\Logs\performance-$(Get-Date -Format 'yyyyMMdd-HHmmss').csv"

# 创建CSV标题
"Timestamp,CPU_Percent,Memory_MB,Network_KB_Sec,Disk_Read_KB_Sec,Disk_Write_KB_Sec,Redis_Memory_MB,Active_Connections" | Out-File $logFile

Write-Host "开始性能监控，持续时间: $DurationMinutes 分钟，间隔: $IntervalSeconds 秒" -ForegroundColor Green
Write-Host "日志文件: $logFile" -ForegroundColor Yellow

while ((Get-Date) -lt $endTime) {
    try {
        # CPU使用率
        $cpu = Get-Counter '\Processor(_Total)\% Processor Time' | Select-Object -ExpandProperty CounterSamples | Select-Object -ExpandProperty CookedValue
        
        # 内存使用
        $memory = Get-Process -Name "IndustryMonitor.Server" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty WorkingSet64
        $memoryMB = [math]::Round($memory / 1MB, 2)
        
        # 网络流量
        $network = Get-Counter '\Network Interface(*)\Bytes Total/sec' | Select-Object -ExpandProperty CounterSamples | Measure-Object -Property CookedValue -Sum | Select-Object -ExpandProperty Sum
        $networkKB = [math]::Round($network / 1KB, 2)
        
        # 磁盘IO
        $diskRead = Get-Counter '\PhysicalDisk(_Total)\Disk Read Bytes/sec' | Select-Object -ExpandProperty CounterSamples | Select-Object -ExpandProperty CookedValue
        $diskWrite = Get-Counter '\PhysicalDisk(_Total)\Disk Write Bytes/sec' | Select-Object -ExpandProperty CounterSamples | Select-Object -ExpandProperty CookedValue
        
        # Redis内存使用
        $redisInfo = redis-cli info memory | Select-String "used_memory:" | ForEach-Object { ($_ -split ":")[1] }
        $redisMemoryMB = [math]::Round([int]$redisInfo / 1MB, 2)
        
        # TCP连接数
        $connections = (netstat -an | Select-String ":5000.*ESTABLISHED").Count
        
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $logEntry = "$timestamp,$([math]::Round($cpu, 2)),$memoryMB,$networkKB,$([math]::Round($diskRead/1KB, 2)),$([math]::Round($diskWrite/1KB, 2)),$redisMemoryMB,$connections"
        
        $logEntry | Out-File $logFile -Append
        Write-Host "$timestamp - CPU: $([math]::Round($cpu, 1))%, 内存: ${memoryMB}MB, 连接: $connections" -ForegroundColor Cyan
        
        Start-Sleep $IntervalSeconds
    }
    catch {
        Write-Host "监控异常: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "性能监控完成，日志保存到: $logFile" -ForegroundColor Green
```

---

## 8. 维护指南

### 8.1 日常维护任务

#### 数据库备份
```powershell
# database-backup.ps1 - 数据库备份脚本
param(
    [string]$BackupPath = "C:\IndustryMonitor\Backup",
    [int]$RetentionDays = 30
)

$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$backupFile = "$BackupPath\industry-$timestamp.db"

try {
    # 确保备份目录存在
    if (-not (Test-Path $BackupPath)) {
        New-Item -Path $BackupPath -ItemType Directory -Force
    }
    
    # 备份数据库
    Write-Host "开始备份数据库..." -ForegroundColor Green
    Copy-Item "C:\IndustryMonitor\Database\industry.db" $backupFile
    
    # 验证备份文件
    if (Test-Path $backupFile) {
        $size = (Get-Item $backupFile).Length
        Write-Host "备份完成: $backupFile (大小: $([math]::Round($size/1MB, 2)) MB)" -ForegroundColor Green
        
        # 清理过期备份
        Write-Host "清理过期备份文件..." -ForegroundColor Yellow
        Get-ChildItem $BackupPath -Filter "industry-*.db" | 
            Where-Object {$_.LastWriteTime -lt (Get-Date).AddDays(-$RetentionDays)} |
            Remove-Item -Force
        
        $remainingBackups = (Get-ChildItem $BackupPath -Filter "industry-*.db").Count
        Write-Host "当前保留备份文件数量: $remainingBackups" -ForegroundColor Cyan
    }
    else {
        throw "备份文件未创建成功"
    }
}
catch {
    Write-Host "备份失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
```

#### 日志清理
```powershell
# log-cleanup.ps1 - 日志清理脚本
param(
    [string]$LogPath = "C:\IndustryMonitor\Logs",
    [int]$RetentionDays = 7,
    [long]$MaxSizeMB = 1000
)

Write-Host "开始清理日志文件..." -ForegroundColor Green
Write-Host "目录: $LogPath" -ForegroundColor Yellow
Write-Host "保留天数: $RetentionDays" -ForegroundColor Yellow
Write-Host "最大总大小: ${MaxSizeMB}MB" -ForegroundColor Yellow

try {
    # 按时间清理
    $cutoffDate = (Get-Date).AddDays(-$RetentionDays)
    $oldFiles = Get-ChildItem $LogPath -Filter "*.log" | Where-Object {$_.LastWriteTime -lt $cutoffDate}
    
    if ($oldFiles) {
        $oldFiles | Remove-Item -Force
        Write-Host "删除了 $($oldFiles.Count) 个过期日志文件" -ForegroundColor Green
    }
    
    # 按大小清理
    $totalSize = (Get-ChildItem $LogPath -Filter "*.log" | Measure-Object -Property Length -Sum).Sum
    $totalSizeMB = [math]::Round($totalSize / 1MB, 2)
    
    Write-Host "当前日志总大小: ${totalSizeMB}MB" -ForegroundColor Cyan
    
    if ($totalSizeMB -gt $MaxSizeMB) {
        Write-Host "日志大小超过限制，删除最旧的文件..." -ForegroundColor Yellow
        
        $files = Get-ChildItem $LogPath -Filter "*.log" | Sort-Object LastWriteTime
        foreach ($file in $files) {
            Remove-Item $file.FullName -Force
            $totalSize -= $file.Length
            $totalSizeMB = [math]::Round($totalSize / 1MB, 2)
            
            Write-Host "删除: $($file.Name) (剩余: ${totalSizeMB}MB)" -ForegroundColor Yellow
            
            if ($totalSizeMB -le $MaxSizeMB) {
                break
            }
        }
    }
    
    Write-Host "日志清理完成" -ForegroundColor Green
}
catch {
    Write-Host "日志清理失败: $($_.Exception.Message)" -ForegroundColor Red
}
```

### 8.2 系统更新

#### 服务端更新脚本
```powershell
# update-server.ps1 - 服务端更新脚本
param(
    [string]$UpdatePackage,
    [switch]$BackupFirst = $true
)

if (-not $UpdatePackage -or -not (Test-Path $UpdatePackage)) {
    Write-Host "请指定有效的更新包路径" -ForegroundColor Red
    exit 1
}

Write-Host "开始更新服务端..." -ForegroundColor Green

try {
    # 创建备份
    if ($BackupFirst) {
        Write-Host "创建当前版本备份..." -ForegroundColor Yellow
        $backupDir = "C:\IndustryMonitor\Backup\Server-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
        Copy-Item "C:\IndustryMonitor\Server" $backupDir -Recurse -Force
        Write-Host "备份创建完成: $backupDir" -ForegroundColor Green
    }
    
    # 停止服务
    Write-Host "停止服务..." -ForegroundColor Yellow
    Stop-Service IndustryMonitorServer -Force
    Start-Sleep -Seconds 5
    
    # 解压更新包
    Write-Host "解压更新包..." -ForegroundColor Yellow
    Expand-Archive -Path $UpdatePackage -DestinationPath "C:\IndustryMonitor\Server" -Force
    
    # 启动服务
    Write-Host "启动服务..." -ForegroundColor Yellow
    Start-Service IndustryMonitorServer
    Start-Sleep -Seconds 10
    
    # 验证更新
    $service = Get-Service IndustryMonitorServer
    if ($service.Status -eq "Running") {
        Write-Host "服务端更新成功!" -ForegroundColor Green
        
        # 测试API
        try {
            $response = Invoke-RestMethod -Uri "http://localhost:5000/api/health" -Method GET -TimeoutSec 30
            Write-Host "API测试通过" -ForegroundColor Green
        }
        catch {
            Write-Host "警告: API测试失败，请检查服务状态" -ForegroundColor Yellow
        }
    }
    else {
        throw "服务启动失败，状态: $($service.Status)"
    }
}
catch {
    Write-Host "更新失败: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($BackupFirst) {
        Write-Host "尝试恢复备份..." -ForegroundColor Yellow
        # 恢复逻辑...
    }
    
    exit 1
}
```

### 8.3 监控和告警

#### 健康检查脚本
```powershell
# health-check.ps1 - 系统健康检查
param(
    [string]$EmailTo = "<EMAIL>",
    [string]$SmtpServer = "smtp.company.com",
    [switch]$SendEmail = $false
)

$healthReport = @()
$overallHealth = $true

Write-Host "开始系统健康检查..." -ForegroundColor Green

# 检查Windows服务状态
try {
    $service = Get-Service IndustryMonitorServer -ErrorAction Stop
    if ($service.Status -eq "Running") {
        $healthReport += "✓ Windows服务运行正常"
        Write-Host "✓ Windows服务运行正常" -ForegroundColor Green
    }
    else {
        $healthReport += "✗ Windows服务状态异常: $($service.Status)"
        Write-Host "✗ Windows服务状态异常: $($service.Status)" -ForegroundColor Red
        $overallHealth = $false
    }
}
catch {
    $healthReport += "✗ Windows服务不存在或无法访问"
    Write-Host "✗ Windows服务不存在或无法访问" -ForegroundColor Red
    $overallHealth = $false
}

# 检查HTTP API
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/health" -Method GET -TimeoutSec 10
    $healthReport += "✓ HTTP API响应正常"
    Write-Host "✓ HTTP API响应正常" -ForegroundColor Green
}
catch {
    $healthReport += "✗ HTTP API无响应: $($_.Exception.Message)"
    Write-Host "✗ HTTP API无响应: $($_.Exception.Message)" -ForegroundColor Red
    $overallHealth = $false
}

# 检查Redis连接
try {
    $redisTest = redis-cli ping
    if ($redisTest -eq "PONG") {
        $healthReport += "✓ Redis连接正常"
        Write-Host "✓ Redis连接正常" -ForegroundColor Green
    }
    else {
        $healthReport += "✗ Redis连接异常"
        Write-Host "✗ Redis连接异常" -ForegroundColor Red
        $overallHealth = $false
    }
}
catch {
    $healthReport += "✗ Redis服务不可用: $($_.Exception.Message)"
    Write-Host "✗ Redis服务不可用: $($_.Exception.Message)" -ForegroundColor Red
    $overallHealth = $false
}

# 检查数据库文件
$dbFile = "C:\IndustryMonitor\Database\industry.db"
if (Test-Path $dbFile) {
    $dbSize = (Get-Item $dbFile).Length
    $healthReport += "✓ 数据库文件正常 (大小: $([math]::Round($dbSize/1MB, 2)) MB)"
    Write-Host "✓ 数据库文件正常 (大小: $([math]::Round($dbSize/1MB, 2)) MB)" -ForegroundColor Green
}
else {
    $healthReport += "✗ 数据库文件不存在"
    Write-Host "✗ 数据库文件不存在" -ForegroundColor Red
    $overallHealth = $false
}

# 检查磁盘空间
$drive = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
$freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
if ($freeSpaceGB -gt 5) {
    $healthReport += "✓ 磁盘空间充足 (剩余: ${freeSpaceGB}GB)"
    Write-Host "✓ 磁盘空间充足 (剩余: ${freeSpaceGB}GB)" -ForegroundColor Green
}
else {
    $healthReport += "⚠ 磁盘空间不足 (剩余: ${freeSpaceGB}GB)"
    Write-Host "⚠ 磁盘空间不足 (剩余: ${freeSpaceGB}GB)" -ForegroundColor Yellow
    if ($freeSpaceGB -lt 2) {
        $overallHealth = $false
    }
}

# 检查内存使用
$process = Get-Process -Name "IndustryMonitor.Server" -ErrorAction SilentlyContinue
if ($process) {
    $memoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
    $healthReport += "✓ 进程运行正常 (内存使用: ${memoryMB}MB)"
    Write-Host "✓ 进程运行正常 (内存使用: ${memoryMB}MB)" -ForegroundColor Green
    
    if ($memoryMB -gt 1000) {
        $healthReport += "⚠ 内存使用量较高"
        Write-Host "⚠ 内存使用量较高" -ForegroundColor Yellow
    }
}
else {
    $healthReport += "✗ 服务端进程未运行"
    Write-Host "✗ 服务端进程未运行" -ForegroundColor Red
    $overallHealth = $false
}

# 生成报告
$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
$reportContent = @"
工业设备监控系统健康检查报告
生成时间: $timestamp
总体状态: $(if($overallHealth){"正常"}else{"异常"})

详细检查结果:
$($healthReport -join "`n")
"@

# 保存报告
$reportFile = "C:\IndustryMonitor\Logs\health-check-$(Get-Date -Format 'yyyyMMdd').txt"
$reportContent | Out-File $reportFile -Append

# 发送邮件告警
if ($SendEmail -and -not $overallHealth) {
    try {
        Send-MailMessage -To $EmailTo -From "<EMAIL>" -Subject "系统健康检查异常告警" -Body $reportContent -SmtpServer $SmtpServer
        Write-Host "告警邮件已发送到: $EmailTo" -ForegroundColor Yellow
    }
    catch {
        Write-Host "邮件发送失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n健康检查完成，报告已保存到: $reportFile" -ForegroundColor Cyan

if ($overallHealth) {
    Write-Host "系统运行状态良好!" -ForegroundColor Green
    exit 0
}
else {
    Write-Host "发现系统异常，请及时处理!" -ForegroundColor Red
    exit 1
}
```

---

## 结语

这份部署指南涵盖了系统部署的各个环节，从环境准备到维护管理的完整流程。在实际部署过程中，请根据具体的网络环境和业务需求适当调整配置参数。

如有任何部署问题，请参考故障排查章节或联系技术支持。

**重要提示**: 在生产环境部署前，建议先在测试环境验证所有功能和性能指标。