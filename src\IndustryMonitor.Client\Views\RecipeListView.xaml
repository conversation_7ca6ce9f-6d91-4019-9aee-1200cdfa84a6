<UserControl x:Class="IndustryMonitor.Client.Views.RecipeListView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:IndustryMonitor.Client.Views"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid Background="{DynamicResource BackgroundBrush}">
        <Border Style="{StaticResource CustomCardStyle}" Margin="16">
            <DockPanel>
                <TextBlock DockPanel.Dock="Top" Style="{StaticResource CustomHeaderTextStyle}">配方管理</TextBlock>
                
                <ToolBarTray DockPanel.Dock="Top" IsLocked="True">
                    <ToolBar Style="{StaticResource MaterialDesignToolBar}">
                        <Button Style="{StaticResource CustomPrimaryButtonStyle}" Content="新建配方" />
                        <Separator />
                        <Button Style="{StaticResource CustomButtonStyle}" Content="编辑配方" />
                        <Button Style="{StaticResource CustomButtonStyle}" Content="删除配方" />
                        <Separator />
                        <Button Style="{StaticResource CustomButtonStyle}" Content="锁定配方" />
                        <Button Style="{StaticResource CustomButtonStyle}" Content="解锁配方" />
                    </ToolBar>
                </ToolBarTray>
                
                <DataGrid x:Name="RecipesDataGrid" 
                          AutoGenerateColumns="False" 
                          IsReadOnly="True"
                          ItemsSource="{Binding Recipes}"
                          Style="{StaticResource MaterialDesignDataGrid}"
                          Margin="0,16,0,0">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="ID" Binding="{Binding Id}">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CustomTextStyle}">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="Name" Binding="{Binding Name}">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CustomTextStyle}">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="Version" Binding="{Binding Version}">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CustomTextStyle}">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="Description" Binding="{Binding Description}">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CustomTextStyle}">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="Created At" Binding="{Binding CreatedAt}">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CustomTextStyle}">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="Is Locked" Binding="{Binding IsLocked}">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CustomTextStyle}">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </DockPanel>
        </Border>
    </Grid>
</UserControl>