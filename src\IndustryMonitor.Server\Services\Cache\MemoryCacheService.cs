using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using IndustryMonitor.Shared.Models.Configs;
using System.Collections.Concurrent;

namespace IndustryMonitor.Server.Services.Cache;

/// <summary>
/// 内存缓存服务实现
/// </summary>
public class MemoryCacheService : IMemoryCacheService, IDisposable
{
    private readonly IMemoryCache _memoryCache;
    private readonly SystemConfig _config;
    private readonly ILogger<MemoryCacheService> _logger;
    private readonly ConcurrentDictionary<string, DateTime> _keyExpirations = new();
    private readonly Timer _cleanupTimer;
    private bool _disposed = false;

    public int Count => _keyExpirations.Count;

    public MemoryCacheService(
        IMemoryCache memoryCache,
        IOptions<SystemConfig> config,
        ILogger<MemoryCacheService> logger)
    {
        _memoryCache = memoryCache;
        _config = config.Value;
        _logger = logger;

        // 启动清理过期键的定时器
        _cleanupTimer = new Timer(CleanupExpiredKeys, null, 
            TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
    }

    public T? Get<T>(string key) where T : class
    {
        try
        {
            if (_memoryCache.TryGetValue(GetKey(key), out var value) && value is T typedValue)
            {
                _logger.LogTrace("内存缓存命中: Key = {Key}", key);
                return typedValue;
            }

            _logger.LogTrace("内存缓存未命中: Key = {Key}", key);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从内存缓存获取数据失败: Key = {Key}", key);
            return null;
        }
    }

    public void Set<T>(string key, T value, TimeSpan? expiration = null) where T : class
    {
        try
        {
            if (value == null)
                return;

            var cacheKey = GetKey(key);
            var options = new MemoryCacheEntryOptions();

            var expirationTime = expiration ?? TimeSpan.FromMinutes(_config.MemoryCacheExpirationMinutes);
            options.SetAbsoluteExpiration(expirationTime);
            
            // 设置缓存项被移除时的回调
            options.RegisterPostEvictionCallback(OnCacheItemEvicted);

            _memoryCache.Set(cacheKey, value, options);
            _keyExpirations[cacheKey] = DateTime.UtcNow.Add(expirationTime);

            _logger.LogTrace("设置内存缓存: Key = {Key}, 过期时间 = {Expiration} 分钟", 
                key, expirationTime.TotalMinutes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置内存缓存失败: Key = {Key}", key);
        }
    }

    public bool Remove(string key)
    {
        try
        {
            var cacheKey = GetKey(key);
            _memoryCache.Remove(cacheKey);
            _keyExpirations.TryRemove(cacheKey, out _);
            
            _logger.LogTrace("删除内存缓存: Key = {Key}", key);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除内存缓存失败: Key = {Key}", key);
            return false;
        }
    }

    public bool Exists(string key)
    {
        try
        {
            return _memoryCache.TryGetValue(GetKey(key), out _);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查内存缓存存在性失败: Key = {Key}", key);
            return false;
        }
    }

    public void Clear()
    {
        try
        {
            // 注意：IMemoryCache 没有提供清空所有缓存的方法
            // 我们只能记录并清理我们管理的键
            var keys = _keyExpirations.Keys.ToList();
            
            foreach (var key in keys)
            {
                _memoryCache.Remove(key);
                _keyExpirations.TryRemove(key, out _);
            }

            _logger.LogInformation("清空内存缓存，共清理 {Count} 个缓存项", keys.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清空内存缓存失败");
        }
    }

    private void OnCacheItemEvicted(object key, object? value, EvictionReason reason, object? state)
    {
        if (key is string stringKey)
        {
            _keyExpirations.TryRemove(stringKey, out _);
            
            if (reason == EvictionReason.Expired)
            {
                _logger.LogTrace("内存缓存项已过期: Key = {Key}", stringKey);
            }
            else if (reason == EvictionReason.Capacity)
            {
                _logger.LogTrace("内存缓存项因容量限制被移除: Key = {Key}", stringKey);
            }
        }
    }

    private void CleanupExpiredKeys(object? state)
    {
        try
        {
            var now = DateTime.UtcNow;
            var expiredKeys = _keyExpirations
                .Where(kv => kv.Value <= now)
                .Select(kv => kv.Key)
                .ToList();

            foreach (var key in expiredKeys)
            {
                _memoryCache.Remove(key);
                _keyExpirations.TryRemove(key, out _);
            }

            if (expiredKeys.Count > 0)
            {
                _logger.LogTrace("清理过期内存缓存项: {Count} 个", expiredKeys.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期内存缓存项时发生错误");
        }
    }

    private string GetKey(string key)
    {
        return $"memory:{key}";
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _cleanupTimer?.Dispose();
            _disposed = true;
        }
    }
}