using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using IndustryMonitor.Server.Services.Auth;
using IndustryMonitor.Shared.Models.DTOs;
using System.ComponentModel.DataAnnotations;

namespace IndustryMonitor.Server.Controllers.Api.v1;

/// <summary>
/// 认证管理 API 控制器
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IAuthService _authService;
    private readonly ILogger<AuthController> _logger;

    public AuthController(IAuthService authService, ILogger<AuthController> logger)
    {
        _authService = authService;
        _logger = logger;
    }

    /// <summary>
    /// 用户登录
    /// </summary>
    [HttpPost("login")]
    public async Task<ActionResult<ApiResponse<LoginResult>>> Login([FromBody] LoginRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<object>.Error("输入数据无效", GetModelStateErrors()));
            }

            // 获取客户端IP地址
            request.IpAddress = GetClientIpAddress();

            var result = await _authService.LoginAsync(request);
            
            if (result.Success)
            {
                return Ok(ApiResponse<LoginResult>.Success(result, result.Message));
            }
            else
            {
                return BadRequest(ApiResponse<object>.Error(result.Message));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户登录处理失败: Username = {Username}", request.Username);
            return StatusCode(500, ApiResponse<object>.Error("登录失败，请稍后重试"));
        }
    }

    /// <summary>
    /// 刷新访问令牌
    /// </summary>
    [HttpPost("refresh")]
    public async Task<ActionResult<ApiResponse<RefreshTokenResult>>> RefreshToken([FromBody] RefreshTokenRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.RefreshToken))
            {
                return BadRequest(ApiResponse<object>.Error("刷新令牌不能为空"));
            }

            var result = await _authService.RefreshTokenAsync(request.RefreshToken);
            
            if (result.Success)
            {
                return Ok(ApiResponse<RefreshTokenResult>.Success(result, result.Message));
            }
            else
            {
                return BadRequest(ApiResponse<object>.Error(result.Message));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新令牌失败");
            return StatusCode(500, ApiResponse<object>.Error("刷新令牌失败，请重新登录"));
        }
    }

    /// <summary>
    /// 用户退出登录
    /// </summary>
    [HttpPost("logout")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<object>>> Logout()
    {
        try
        {
            var username = User.Identity?.Name;
            if (!string.IsNullOrEmpty(username))
            {
                await _authService.LogoutAsync(username);
            }

            return Ok(ApiResponse<object>.Success(null, "退出登录成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户退出登录处理失败");
            return StatusCode(500, ApiResponse<object>.Error("退出登录失败"));
        }
    }

    /// <summary>
    /// 获取当前用户信息
    /// </summary>
    [HttpGet("me")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<UserDto>>> GetCurrentUser()
    {
        try
        {
            var username = User.Identity?.Name;
            if (string.IsNullOrEmpty(username))
            {
                return Unauthorized(ApiResponse<object>.Error("用户未认证"));
            }

            var user = await _authService.GetCurrentUserAsync(username);
            return Ok(ApiResponse<UserDto>.Success(user, "获取当前用户信息成功"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "获取当前用户信息业务逻辑错误");
            return NotFound(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取当前用户信息失败");
            return StatusCode(500, ApiResponse<object>.Error("获取用户信息失败"));
        }
    }

    /// <summary>
    /// 修改密码
    /// </summary>
    [HttpPost("change-password")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<bool>>> ChangePassword([FromBody] ChangePasswordRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<object>.Error("输入数据无效", GetModelStateErrors()));
            }

            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
            {
                return Unauthorized(ApiResponse<object>.Error("用户认证信息无效"));
            }

            var result = await _authService.ChangePasswordAsync(userId, request);
            
            if (result)
            {
                return Ok(ApiResponse<bool>.Success(true, "密码修改成功"));
            }
            else
            {
                return BadRequest(ApiResponse<object>.Error("原密码不正确"));
            }
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "修改密码业务逻辑错误");
            return BadRequest(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "修改密码失败");
            return StatusCode(500, ApiResponse<object>.Error("修改密码失败"));
        }
    }

    /// <summary>
    /// 验证令牌有效性
    /// </summary>
    [HttpPost("validate-token")]
    public async Task<ActionResult<ApiResponse<bool>>> ValidateToken([FromBody] ValidateTokenRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.Token))
            {
                return BadRequest(ApiResponse<object>.Error("令牌不能为空"));
            }

            var isValid = await _authService.ValidateTokenAsync(request.Token);
            var message = isValid ? "令牌有效" : "令牌无效或已过期";
            
            return Ok(ApiResponse<bool>.Success(isValid, message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证令牌失败");
            return StatusCode(500, ApiResponse<object>.Error("验证令牌失败"));
        }
    }

    private string GetClientIpAddress()
    {
        var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
        
        // 检查是否存在代理转发的真实IP
        if (HttpContext.Request.Headers.TryGetValue("X-Forwarded-For", out var forwardedFor))
        {
            var ips = forwardedFor.ToString().Split(',');
            if (ips.Length > 0 && !string.IsNullOrWhiteSpace(ips[0]))
            {
                ipAddress = ips[0].Trim();
            }
        }
        else if (HttpContext.Request.Headers.TryGetValue("X-Real-IP", out var realIp))
        {
            if (!string.IsNullOrWhiteSpace(realIp))
            {
                ipAddress = realIp.ToString().Trim();
            }
        }

        return ipAddress ?? "Unknown";
    }

    private Dictionary<string, string[]> GetModelStateErrors()
    {
        return ModelState
            .Where(x => x.Value?.Errors?.Count > 0)
            .ToDictionary(
                kvp => kvp.Key,
                kvp => kvp.Value?.Errors?.Select(e => e.ErrorMessage).ToArray() ?? Array.Empty<string>()
            );
    }
}

/// <summary>
/// 刷新令牌请求
/// </summary>
public class RefreshTokenRequest
{
    [Required(ErrorMessage = "刷新令牌不能为空")]
    public string RefreshToken { get; set; } = "";
}

/// <summary>
/// 验证令牌请求
/// </summary>
public class ValidateTokenRequest
{
    [Required(ErrorMessage = "令牌不能为空")]
    public string Token { get; set; } = "";
}