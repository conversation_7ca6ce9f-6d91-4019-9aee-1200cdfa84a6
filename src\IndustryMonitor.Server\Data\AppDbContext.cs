using Microsoft.EntityFrameworkCore;
using IndustryMonitor.Shared.Models.Entities;

namespace IndustryMonitor.Server.Data;

public class AppDbContext : DbContext
{
    public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
    {
    }

    // DbSets
    public DbSet<User> Users { get; set; }
    public DbSet<Device> Devices { get; set; }
    public DbSet<Recipe> Recipes { get; set; }
    public DbSet<WriteTask> WriteTasks { get; set; }
    public DbSet<OperationLog> OperationLogs { get; set; }
    public DbSet<DeviceHistory> DeviceHistories { get; set; }
    public DbSet<RecipeHistory> RecipeHistories { get; set; }
    public DbSet<SystemConfig> SystemConfigs { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        ConfigureUserEntity(modelBuilder);
        ConfigureDeviceEntity(modelBuilder);
        ConfigureRecipeEntity(modelBuilder);
        ConfigureWriteTaskEntity(modelBuilder);
        ConfigureOperationLogEntity(modelBuilder);
        ConfigureDeviceHistoryEntity(modelBuilder);
        ConfigureRecipeHistoryEntity(modelBuilder);
        ConfigureSystemConfigEntity(modelBuilder);

        SeedData(modelBuilder);
    }

    private void ConfigureUserEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.Username).IsUnique();
            entity.Property(e => e.Username).IsRequired().HasMaxLength(50);
            entity.Property(e => e.PasswordHash).IsRequired().HasMaxLength(255);
            entity.Property(e => e.FullName).HasMaxLength(100);
            entity.Property(e => e.Role).HasConversion<int>();
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("datetime('now')");
        });
    }

    private void ConfigureDeviceEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Device>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.IpAddress).IsUnique();
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.IpAddress).IsRequired().HasMaxLength(15);
            entity.Property(e => e.DeviceType).HasMaxLength(50);
            entity.Property(e => e.Location).HasMaxLength(100);
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("datetime('now')");
        });
    }

    private void ConfigureRecipeEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Recipe>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Description).HasMaxLength(1000);
            entity.Property(e => e.Content).IsRequired();
            entity.Property(e => e.Version).HasMaxLength(20).HasDefaultValue("1.0");
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity.Property(e => e.UpdatedBy).HasMaxLength(50);
            entity.Property(e => e.LockedBy).HasMaxLength(50);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("datetime('now')");

            // 外键关系
            entity.HasOne(e => e.CreatedByUser)
                  .WithMany(u => u.CreatedRecipes)
                  .HasForeignKey(e => e.CreatedByUserId)
                  .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private void ConfigureWriteTaskEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<WriteTask>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.TaskId).IsUnique();
            entity.Property(e => e.TaskId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.DeviceIds).IsRequired();
            entity.Property(e => e.Status).HasConversion<int>();
            entity.Property(e => e.Error).HasMaxLength(1000);
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");

            // 外键关系
            entity.HasOne(e => e.Recipe)
                  .WithMany(r => r.WriteTasks)
                  .HasForeignKey(e => e.RecipeId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.CreatedByUser)
                  .WithMany(u => u.WriteTasks)
                  .HasForeignKey(e => e.CreatedByUserId)
                  .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private void ConfigureOperationLogEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<OperationLog>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.UserName).HasMaxLength(50);
            entity.Property(e => e.Operation).HasConversion<int>();
            entity.Property(e => e.Target).HasMaxLength(200);
            entity.Property(e => e.Details).HasMaxLength(1000);
            entity.Property(e => e.IpAddress).HasMaxLength(50);
            entity.Property(e => e.Result).HasMaxLength(20).HasDefaultValue("Success");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");

            // 外键关系
            entity.HasOne(e => e.User)
                  .WithMany(u => u.OperationLogs)
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private void ConfigureDeviceHistoryEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<DeviceHistory>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Data).IsRequired();
            entity.Property(e => e.Quality).HasConversion<int>();
            entity.Property(e => e.Timestamp).HasDefaultValueSql("datetime('now')");

            // 外键关系
            entity.HasOne(e => e.Device)
                  .WithMany(d => d.DeviceHistories)
                  .HasForeignKey(e => e.DeviceId)
                  .OnDelete(DeleteBehavior.Cascade);

            // 索引
            entity.HasIndex(e => new { e.DeviceId, e.Timestamp });
        });
    }

    private void ConfigureRecipeHistoryEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<RecipeHistory>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Version).HasMaxLength(20);
            entity.Property(e => e.ChangeType).HasMaxLength(20);
            entity.Property(e => e.ChangeDescription).HasMaxLength(500);
            entity.Property(e => e.ChangedBy).HasMaxLength(50);
            entity.Property(e => e.ChangedAt).HasDefaultValueSql("datetime('now')");

            // 外键关系
            entity.HasOne(e => e.Recipe)
                  .WithMany(r => r.RecipeHistories)
                  .HasForeignKey(e => e.RecipeId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private void ConfigureSystemConfigEntity(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<SystemConfig>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.Key).IsUnique();
            entity.Property(e => e.Key).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Description).HasMaxLength(200);
            entity.Property(e => e.UpdatedBy).HasMaxLength(50);
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("datetime('now')");
        });
    }

    private void SeedData(ModelBuilder modelBuilder)
    {
        // 种子数据将在 Program.cs 中通过代码添加
        // 这里可以添加一些静态配置数据
        modelBuilder.Entity<SystemConfig>().HasData(
            new SystemConfig { Id = 1, Key = "SystemVersion", Value = "1.0.0", Description = "系统版本号" },
            new SystemConfig { Id = 2, Key = "DatabaseVersion", Value = "1.0.0", Description = "数据库版本号" }
        );
    }

    public override int SaveChanges()
    {
        UpdateTimestamps();
        return base.SaveChanges();
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateTimestamps();
        return await base.SaveChangesAsync(cancellationToken);
    }

    private void UpdateTimestamps()
    {
        var entries = ChangeTracker.Entries()
            .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

        foreach (var entry in entries)
        {
            if (entry.Entity is User user)
            {
                if (entry.State == EntityState.Added)
                    user.CreatedAt = DateTime.UtcNow;
                user.UpdatedAt = DateTime.UtcNow;
            }
            else if (entry.Entity is Device device)
            {
                if (entry.State == EntityState.Added)
                    device.CreatedAt = DateTime.UtcNow;
                device.UpdatedAt = DateTime.UtcNow;
            }
            else if (entry.Entity is Recipe recipe)
            {
                if (entry.State == EntityState.Added)
                    recipe.CreatedAt = DateTime.UtcNow;
                recipe.UpdatedAt = DateTime.UtcNow;
            }
        }
    }
}