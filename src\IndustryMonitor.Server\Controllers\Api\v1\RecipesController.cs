using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using IndustryMonitor.Server.Services.Recipe;
using IndustryMonitor.Shared.Models.DTOs;
using System.ComponentModel.DataAnnotations;

namespace IndustryMonitor.Server.Controllers.Api.v1;

/// <summary>
/// 配方管理 API 控制器
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class RecipesController : ControllerBase
{
    private readonly IRecipeService _recipeService;
    private readonly ILogger<RecipesController> _logger;

    public RecipesController(IRecipeService recipeService, ILogger<RecipesController> logger)
    {
        _recipeService = recipeService;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有配方
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<IEnumerable<RecipeDto>>>> GetAllRecipes()
    {
        try
        {
            var recipes = await _recipeService.GetAllRecipesAsync();
            return Ok(ApiResponse<IEnumerable<RecipeDto>>.Success(recipes, "获取配方列表成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有配方失败");
            return StatusCode(500, ApiResponse<object>.Error("获取配方列表失败"));
        }
    }

    /// <summary>
    /// 获取未锁定的配方
    /// </summary>
    [HttpGet("unlocked")]
    public async Task<ActionResult<ApiResponse<IEnumerable<RecipeDto>>>> GetUnlockedRecipes()
    {
        try
        {
            var recipes = await _recipeService.GetUnlockedRecipesAsync();
            return Ok(ApiResponse<IEnumerable<RecipeDto>>.Success(recipes, "获取可用配方列表成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取未锁定配方失败");
            return StatusCode(500, ApiResponse<object>.Error("获取可用配方列表失败"));
        }
    }

    /// <summary>
    /// 根据ID获取配方
    /// </summary>
    [HttpGet("{id:int}")]
    public async Task<ActionResult<ApiResponse<RecipeDto>>> GetRecipe(int id)
    {
        try
        {
            var recipe = await _recipeService.GetRecipeAsync(id);
            if (recipe == null)
            {
                return NotFound(ApiResponse<object>.Error("配方不存在"));
            }

            return Ok(ApiResponse<RecipeDto>.Success(recipe, "获取配方信息成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配方失败: RecipeId = {RecipeId}", id);
            return StatusCode(500, ApiResponse<object>.Error("获取配方信息失败"));
        }
    }

    /// <summary>
    /// 创建配方
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,Operator")]
    public async Task<ActionResult<ApiResponse<RecipeDto>>> CreateRecipe([FromBody] CreateRecipeRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<object>.Error("输入数据无效", GetModelStateErrors()));
            }

            // 从当前用户获取创建者信息
            request.CreatedBy = User.Identity?.Name ?? "Anonymous";

            var recipe = await _recipeService.CreateRecipeAsync(request);
            return CreatedAtAction(nameof(GetRecipe), new { id = recipe.Id }, 
                ApiResponse<RecipeDto>.Success(recipe, "创建配方成功"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "创建配方业务逻辑错误");
            return BadRequest(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建配方失败");
            return StatusCode(500, ApiResponse<object>.Error("创建配方失败"));
        }
    }

    /// <summary>
    /// 更新配方
    /// </summary>
    [HttpPut("{id:int}")]
    [Authorize(Roles = "Admin,Operator")]
    public async Task<ActionResult<ApiResponse<RecipeDto>>> UpdateRecipe(int id, [FromBody] UpdateRecipeRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<object>.Error("输入数据无效", GetModelStateErrors()));
            }

            // 从当前用户获取更新者信息
            request.UpdatedBy = User.Identity?.Name ?? "Anonymous";

            var recipe = await _recipeService.UpdateRecipeAsync(id, request);
            return Ok(ApiResponse<RecipeDto>.Success(recipe, "更新配方成功"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "更新配方业务逻辑错误: RecipeId = {RecipeId}", id);
            return BadRequest(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新配方失败: RecipeId = {RecipeId}", id);
            return StatusCode(500, ApiResponse<object>.Error("更新配方失败"));
        }
    }

    /// <summary>
    /// 删除配方
    /// </summary>
    [HttpDelete("{id:int}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<object>>> DeleteRecipe(int id)
    {
        try
        {
            await _recipeService.DeleteRecipeAsync(id);
            return Ok(ApiResponse<object>.Success(null, "删除配方成功"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "删除配方业务逻辑错误: RecipeId = {RecipeId}", id);
            return BadRequest(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除配方失败: RecipeId = {RecipeId}", id);
            return StatusCode(500, ApiResponse<object>.Error("删除配方失败"));
        }
    }

    /// <summary>
    /// 锁定配方
    /// </summary>
    [HttpPost("{id:int}/lock")]
    [Authorize(Roles = "Admin,Operator")]
    public async Task<ActionResult<ApiResponse<bool>>> LockRecipe(int id)
    {
        try
        {
            var lockedBy = User.Identity?.Name ?? "Anonymous";
            var result = await _recipeService.LockRecipeAsync(id, lockedBy);
            
            var message = result ? "配方锁定成功" : "配方锁定失败，可能已被其他用户锁定";
            var statusCode = result ? 200 : 409; // Conflict
            
            return StatusCode(statusCode, ApiResponse<bool>.Success(result, message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "锁定配方失败: RecipeId = {RecipeId}", id);
            return StatusCode(500, ApiResponse<object>.Error("锁定配方失败"));
        }
    }

    /// <summary>
    /// 解锁配方
    /// </summary>
    [HttpPost("{id:int}/unlock")]
    [Authorize(Roles = "Admin,Operator")]
    public async Task<ActionResult<ApiResponse<bool>>> UnlockRecipe(int id)
    {
        try
        {
            var unlockedBy = User.Identity?.Name ?? "Anonymous";
            var result = await _recipeService.UnlockRecipeAsync(id, unlockedBy);
            
            var message = result ? "配方解锁成功" : "配方解锁失败";
            return Ok(ApiResponse<bool>.Success(result, message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解锁配方失败: RecipeId = {RecipeId}", id);
            return StatusCode(500, ApiResponse<object>.Error("解锁配方失败"));
        }
    }

    /// <summary>
    /// 克隆配方
    /// </summary>
    [HttpPost("{id:int}/clone")]
    [Authorize(Roles = "Admin,Operator")]
    public async Task<ActionResult<ApiResponse<RecipeDto>>> CloneRecipe(int id, [FromBody] CloneRecipeRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<object>.Error("输入数据无效", GetModelStateErrors()));
            }

            var createdBy = User.Identity?.Name ?? "Anonymous";
            var recipe = await _recipeService.CloneRecipeAsync(id, request.NewName, createdBy);
            
            if (recipe == null)
            {
                return BadRequest(ApiResponse<object>.Error("克隆配方失败，源配方不存在"));
            }

            return CreatedAtAction(nameof(GetRecipe), new { id = recipe.Id }, 
                ApiResponse<RecipeDto>.Success(recipe, "克隆配方成功"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "克隆配方业务逻辑错误: RecipeId = {RecipeId}", id);
            return BadRequest(ApiResponse<object>.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "克隆配方失败: RecipeId = {RecipeId}", id);
            return StatusCode(500, ApiResponse<object>.Error("克隆配方失败"));
        }
    }

    /// <summary>
    /// 验证配方内容
    /// </summary>
    [HttpPost("validate")]
    [Authorize(Roles = "Admin,Operator")]
    public async Task<ActionResult<ApiResponse<bool>>> ValidateRecipeContent([FromBody] ValidateRecipeRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.Content))
            {
                return BadRequest(ApiResponse<object>.Error("配方内容不能为空"));
            }

            var isValid = await _recipeService.ValidateRecipeContentAsync(request.Content);
            var message = isValid ? "配方内容格式正确" : "配方内容格式不正确";
            
            return Ok(ApiResponse<bool>.Success(isValid, message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证配方内容失败");
            return StatusCode(500, ApiResponse<object>.Error("验证配方内容失败"));
        }
    }

    /// <summary>
    /// 获取配方历史记录
    /// </summary>
    [HttpGet("{id:int}/history")]
    public async Task<ActionResult<ApiResponse<IEnumerable<RecipeHistoryDto>>>> GetRecipeHistory(int id)
    {
        try
        {
            var history = await _recipeService.GetRecipeHistoryAsync(id);
            return Ok(ApiResponse<IEnumerable<RecipeHistoryDto>>.Success(history, "获取配方历史记录成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取配方历史记录失败: RecipeId = {RecipeId}", id);
            return StatusCode(500, ApiResponse<object>.Error("获取配方历史记录失败"));
        }
    }

    private Dictionary<string, string[]> GetModelStateErrors()
    {
        return ModelState
            .Where(x => x.Value?.Errors?.Count > 0)
            .ToDictionary(
                kvp => kvp.Key,
                kvp => kvp.Value?.Errors?.Select(e => e.ErrorMessage).ToArray() ?? Array.Empty<string>()
            );
    }
}

/// <summary>
/// 克隆配方请求
/// </summary>
public class CloneRecipeRequest
{
    [Required(ErrorMessage = "新配方名称不能为空")]
    [StringLength(100, MinimumLength = 2, ErrorMessage = "配方名称长度必须在2-100个字符之间")]
    public string NewName { get; set; } = "";
}

/// <summary>
/// 验证配方内容请求
/// </summary>
public class ValidateRecipeRequest
{
    [Required(ErrorMessage = "配方内容不能为空")]
    public string Content { get; set; } = "";
}