using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using IndustryMonitor.Server.Services.Cache;

namespace IndustryMonitor.Server.Hubs;

/// <summary>
/// 设备监控 SignalR Hub
/// 提供设备状态和数据的实时推送
/// </summary>
[Authorize]
public class DeviceMonitorHub : Hub
{
    private readonly ICacheService _cacheService;
    private readonly ILogger<DeviceMonitorHub> _logger;

    public DeviceMonitorHub(
        ICacheService cacheService,
        ILogger<DeviceMonitorHub> logger)
    {
        _cacheService = cacheService;
        _logger = logger;
    }

    public override async Task OnConnectedAsync()
    {
        var username = Context.User?.Identity?.Name ?? "Anonymous";
        var connectionId = Context.ConnectionId;
        
        _logger.LogInformation("设备监控客户端连接: 用户 = {Username}, 连接ID = {ConnectionId}", 
            username, connectionId);
        
        // 发送当前连接状态
        await Clients.Caller.SendAsync("Connected", new
        {
            ConnectionId = connectionId,
            Username = username,
            ConnectedAt = DateTime.UtcNow,
            Message = "已连接到设备监控中心"
        });
        
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var username = Context.User?.Identity?.Name ?? "Anonymous";
        var connectionId = Context.ConnectionId;
        
        _logger.LogInformation("设备监控客户端断开连接: 用户 = {Username}, 连接ID = {ConnectionId}, 原因 = {Reason}", 
            username, connectionId, exception?.Message ?? "正常断开");
        
        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// 加入设备监控组
    /// </summary>
    public async Task JoinDeviceGroup(int deviceId)
    {
        try
        {
            var groupName = $"Device_{deviceId}";
            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
            
            var username = Context.User?.Identity?.Name ?? "Anonymous";
            _logger.LogInformation("用户 {Username} 加入设备组: {GroupName}", username, groupName);
            
            // 发送设备的最新状态
            var deviceData = await _cacheService.GetAsync<object>($"device_data_{deviceId}");
            if (deviceData != null)
            {
                await Clients.Caller.SendAsync("DeviceDataUpdate", deviceData);
            }
            
            await Clients.Caller.SendAsync("JoinedDeviceGroup", new
            {
                DeviceId = deviceId,
                GroupName = groupName,
                JoinedAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加入设备组失败: DeviceId = {DeviceId}", deviceId);
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "加入设备监控组失败",
                Details = ex.Message
            });
        }
    }

    /// <summary>
    /// 离开设备监控组
    /// </summary>
    public async Task LeaveDeviceGroup(int deviceId)
    {
        try
        {
            var groupName = $"Device_{deviceId}";
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
            
            var username = Context.User?.Identity?.Name ?? "Anonymous";
            _logger.LogInformation("用户 {Username} 离开设备组: {GroupName}", username, groupName);
            
            await Clients.Caller.SendAsync("LeftDeviceGroup", new
            {
                DeviceId = deviceId,
                GroupName = groupName,
                LeftAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "离开设备组失败: DeviceId = {DeviceId}", deviceId);
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "离开设备监控组失败",
                Details = ex.Message
            });
        }
    }

    /// <summary>
    /// 加入所有设备监控组
    /// </summary>
    public async Task JoinAllDevicesGroup()
    {
        try
        {
            const string groupName = "AllDevices";
            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
            
            var username = Context.User?.Identity?.Name ?? "Anonymous";
            _logger.LogInformation("用户 {Username} 加入所有设备监控组", username);
            
            await Clients.Caller.SendAsync("JoinedAllDevicesGroup", new
            {
                GroupName = groupName,
                JoinedAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加入所有设备监控组失败");
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "加入所有设备监控组失败",
                Details = ex.Message
            });
        }
    }

    /// <summary>
    /// 离开所有设备监控组
    /// </summary>
    public async Task LeaveAllDevicesGroup()
    {
        try
        {
            const string groupName = "AllDevices";
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
            
            var username = Context.User?.Identity?.Name ?? "Anonymous";
            _logger.LogInformation("用户 {Username} 离开所有设备监控组", username);
            
            await Clients.Caller.SendAsync("LeftAllDevicesGroup", new
            {
                GroupName = groupName,
                LeftAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "离开所有设备监控组失败");
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "离开所有设备监控组失败",
                Details = ex.Message
            });
        }
    }

    /// <summary>
    /// 获取设备实时状态
    /// </summary>
    public async Task GetDeviceStatus(int deviceId)
    {
        try
        {
            var deviceData = await _cacheService.GetAsync<object>($"device_data_{deviceId}");
            
            if (deviceData != null)
            {
                await Clients.Caller.SendAsync("DeviceStatusResponse", new
                {
                    DeviceId = deviceId,
                    Data = deviceData,
                    RequestedAt = DateTime.UtcNow
                });
            }
            else
            {
                await Clients.Caller.SendAsync("DeviceStatusResponse", new
                {
                    DeviceId = deviceId,
                    Data = (object?)null,
                    Message = "设备数据不存在或已过期",
                    RequestedAt = DateTime.UtcNow
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备状态失败: DeviceId = {DeviceId}", deviceId);
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "获取设备状态失败",
                Details = ex.Message
            });
        }
    }

    /// <summary>
    /// 获取系统健康状态
    /// </summary>
    public async Task GetSystemHealth()
    {
        try
        {
            var healthData = await _cacheService.GetAsync<object>("system_health");
            
            await Clients.Caller.SendAsync("SystemHealthResponse", new
            {
                Health = healthData,
                RequestedAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取系统健康状态失败");
            await Clients.Caller.SendAsync("Error", new
            {
                Message = "获取系统健康状态失败",
                Details = ex.Message
            });
        }
    }

    /// <summary>
    /// 发送心跳包
    /// </summary>
    public async Task Heartbeat()
    {
        await Clients.Caller.SendAsync("HeartbeatResponse", new
        {
            Timestamp = DateTime.UtcNow,
            ConnectionId = Context.ConnectionId
        });
    }
}