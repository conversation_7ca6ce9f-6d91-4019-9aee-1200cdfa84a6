# API接口文档

本文档描述了工业设备监控系统的所有REST API接口，包括请求参数、响应格式和使用示例。

## 📋 目录

1. [接口概述](#1-接口概述)
2. [认证与授权](#2-认证与授权)
3. [通用响应格式](#3-通用响应格式)
4. [设备管理接口](#4-设备管理接口)
5. [配方管理接口](#5-配方管理接口)
6. [任务管理接口](#6-任务管理接口)
7. [用户管理接口](#7-用户管理接口)
8. [系统管理接口](#8-系统管理接口)
9. [SignalR实时通信](#9-signalr实时通信)
10. [错误代码说明](#10-错误代码说明)

---

## 1. 接口概述

### 1.1 基本信息

| 项目 | 说明 |
|------|------|
| 基础URL | `http://{server_ip}:5000/api` |
| 协议 | HTTP/1.1 |
| 数据格式 | JSON |
| 字符编码 | UTF-8 |
| 认证方式 | JWT Bearer Token |

### 1.2 请求头要求

```http
Content-Type: application/json
Authorization: Bearer {jwt_token}
Accept: application/json
```

### 1.3 HTTP状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未认证或认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 500 | 服务器内部错误 |

---

## 2. 认证与授权

### 2.1 用户登录

获取访问令牌。

**请求**
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "password123"
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "550e8400-e29b-41d4-a716-446655440000",
    "expiresAt": "2024-01-27T10:30:00Z",
    "user": {
      "id": 1,
      "username": "admin",
      "fullName": "系统管理员",
      "role": "Admin"
    }
  }
}
```

### 2.2 刷新令牌

使用刷新令牌获取新的访问令牌。

**请求**
```http
POST /api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "550e8400-e29b-41d4-a716-446655440000"
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "660e8400-e29b-41d4-a716-446655440001",
    "expiresAt": "2024-01-27T10:30:00Z"
  }
}
```

### 2.3 用户权限

| 角色 | 权限描述 |
|------|----------|
| Admin | 所有权限，包括用户管理、系统配置 |
| Operator | 设备监控、配方管理、任务执行 |
| Viewer | 仅查看权限，无法修改数据 |

---

## 3. 通用响应格式

所有API接口都遵循统一的响应格式：

### 3.1 成功响应

```json
{
  "success": true,
  "data": {
    // 具体的响应数据
  },
  "message": "操作成功",
  "timestamp": "2024-01-20T10:30:45Z"
}
```

### 3.2 错误响应

```json
{
  "success": false,
  "error": {
    "code": "DEVICE_NOT_FOUND",
    "message": "设备不存在",
    "details": "Device with ID 999 was not found"
  },
  "timestamp": "2024-01-20T10:30:45Z"
}
```

### 3.3 分页响应

```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "totalItems": 156,
      "totalPages": 8,
      "hasNextPage": true,
      "hasPreviousPage": false
    }
  }
}
```

---

## 4. 设备管理接口

### 4.1 获取设备列表

获取所有设备的基本信息。

**请求**
```http
GET /api/devices?page=1&pageSize=20&keyword=注塑机&status=online
```

**查询参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | integer | 否 | 页码，默认1 |
| pageSize | integer | 否 | 每页大小，默认20，最大100 |
| keyword | string | 否 | 搜索关键词，匹配设备名称或IP |
| status | string | 否 | 状态筛选：online/offline/error |
| deviceType | string | 否 | 设备类型筛选 |
| location | string | 否 | 位置筛选 |

**响应**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "name": "1号注塑机",
        "ipAddress": "************",
        "port": 502,
        "slaveId": 1,
        "deviceType": "PLC",
        "location": "车间A-01",
        "status": "online",
        "lastUpdate": "2024-01-20T10:29:45Z",
        "registerStart": 400001,
        "registerCount": 10,
        "isActive": true,
        "description": "主生产线1号设备"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "totalItems": 200,
      "totalPages": 10,
      "hasNextPage": true,
      "hasPreviousPage": false
    }
  }
}
```

### 4.2 获取设备详情

获取指定设备的详细信息。

**请求**
```http
GET /api/devices/1
```

**响应**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "1号注塑机",
    "ipAddress": "************",
    "port": 502,
    "slaveId": 1,
    "deviceType": "PLC",
    "location": "车间A-01",
    "status": "online",
    "lastUpdate": "2024-01-20T10:29:45Z",
    "registerStart": 400001,
    "registerCount": 10,
    "isActive": true,
    "description": "主生产线1号设备",
    "connectionInfo": {
      "isConnected": true,
      "lastConnected": "2024-01-20T08:00:00Z",
      "connectionDuration": "PT2H29M45S",
      "errorCount": 0,
      "lastError": null
    },
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-20T09:00:00Z"
  }
}
```

### 4.3 获取设备实时数据

获取指定设备的实时寄存器数据。

**请求**
```http
GET /api/devices/1/realtime
```

**响应**
```json
{
  "success": true,
  "data": {
    "deviceId": 1,
    "deviceName": "1号注塑机",
    "status": "online",
    "quality": "Good",
    "registers": [
      {
        "address": 400001,
        "value": 1250,
        "description": "温度",
        "unit": "°C",
        "timestamp": "2024-01-20T10:29:45Z"
      },
      {
        "address": 400002,
        "value": 85,
        "description": "压力",
        "unit": "bar",
        "timestamp": "2024-01-20T10:29:45Z"
      }
    ],
    "lastUpdate": "2024-01-20T10:29:45Z"
  }
}
```

### 4.4 获取设备历史数据

获取设备的历史数据，支持时间范围查询。

**请求**
```http
GET /api/devices/1/history?startTime=2024-01-20T08:00:00Z&endTime=2024-01-20T10:00:00Z&interval=5m
```

**查询参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| startTime | datetime | 是 | 开始时间 (ISO 8601) |
| endTime | datetime | 是 | 结束时间 (ISO 8601) |
| interval | string | 否 | 数据间隔：1m/5m/15m/1h，默认5m |
| registers | string | 否 | 寄存器地址列表，逗号分隔 |

**响应**
```json
{
  "success": true,
  "data": {
    "deviceId": 1,
    "deviceName": "1号注塑机",
    "timeRange": {
      "startTime": "2024-01-20T08:00:00Z",
      "endTime": "2024-01-20T10:00:00Z",
      "interval": "5m"
    },
    "series": [
      {
        "register": 400001,
        "description": "温度",
        "unit": "°C",
        "dataPoints": [
          {
            "timestamp": "2024-01-20T08:00:00Z",
            "value": 1245,
            "quality": "Good"
          },
          {
            "timestamp": "2024-01-20T08:05:00Z",
            "value": 1248,
            "quality": "Good"
          }
        ]
      }
    ]
  }
}
```

### 4.5 创建设备

添加新设备到系统中。

**请求**
```http
POST /api/devices
Content-Type: application/json
Authorization: Bearer {jwt_token}

{
  "name": "新设备",
  "ipAddress": "************",
  "port": 502,
  "slaveId": 1,
  "deviceType": "PLC",
  "location": "车间B-01",
  "registerStart": 400001,
  "registerCount": 15,
  "description": "新添加的测试设备"
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "id": 201,
    "name": "新设备",
    "ipAddress": "************",
    "port": 502,
    "slaveId": 1,
    "deviceType": "PLC",
    "location": "车间B-01",
    "registerStart": 400001,
    "registerCount": 15,
    "description": "新添加的测试设备",
    "isActive": true,
    "createdAt": "2024-01-20T10:30:00Z"
  },
  "message": "设备创建成功"
}
```

### 4.6 更新设备

更新设备配置信息。

**请求**
```http
PUT /api/devices/1
Content-Type: application/json
Authorization: Bearer {jwt_token}

{
  "name": "1号注塑机(已更新)",
  "location": "车间A-01-新位置",
  "registerCount": 12,
  "description": "更新后的设备描述"
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "1号注塑机(已更新)",
    "ipAddress": "************",
    "port": 502,
    "slaveId": 1,
    "deviceType": "PLC",
    "location": "车间A-01-新位置",
    "registerStart": 400001,
    "registerCount": 12,
    "description": "更新后的设备描述",
    "isActive": true,
    "updatedAt": "2024-01-20T10:35:00Z"
  },
  "message": "设备更新成功"
}
```

### 4.7 删除设备

从系统中删除设备。

**请求**
```http
DELETE /api/devices/201
Authorization: Bearer {jwt_token}
```

**响应**
```json
{
  "success": true,
  "message": "设备删除成功"
}
```

### 4.8 批量操作设备

批量启用/禁用设备。

**请求**
```http
POST /api/devices/batch
Content-Type: application/json
Authorization: Bearer {jwt_token}

{
  "deviceIds": [1, 2, 3, 4, 5],
  "action": "enable"  // enable/disable
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "successCount": 5,
    "failureCount": 0,
    "results": [
      { "deviceId": 1, "success": true },
      { "deviceId": 2, "success": true },
      { "deviceId": 3, "success": true },
      { "deviceId": 4, "success": true },
      { "deviceId": 5, "success": true }
    ]
  },
  "message": "批量操作完成"
}
```

---

## 5. 配方管理接口

### 5.1 获取配方列表

获取所有配方信息。

**请求**
```http
GET /api/recipes?page=1&pageSize=20&keyword=产品A&status=all
```

**查询参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | integer | 否 | 页码，默认1 |
| pageSize | integer | 否 | 每页大小，默认20 |
| keyword | string | 否 | 搜索关键词 |
| status | string | 否 | 状态筛选：all/locked/unlocked |
| createdBy | string | 否 | 创建者筛选 |

**响应**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "name": "产品A标准配方",
        "description": "产品A的标准生产配方",
        "version": "1.2",
        "isLocked": false,
        "lockedBy": null,
        "lockedAt": null,
        "createdBy": "admin",
        "createdAt": "2024-01-15T08:00:00Z",
        "updatedBy": "operator1",
        "updatedAt": "2024-01-20T09:00:00Z",
        "registerCount": 15
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "totalItems": 25,
      "totalPages": 2
    }
  }
}
```

### 5.2 获取配方详情

获取指定配方的详细信息和内容。

**请求**
```http
GET /api/recipes/1
```

**响应**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "产品A标准配方",
    "description": "产品A的标准生产配方",
    "version": "1.2",
    "content": {
      "metadata": {
        "productCode": "PA001",
        "temperature": 180,
        "pressure": 85,
        "cycleTime": 45
      },
      "registers": [
        {
          "address": 400001,
          "value": 1800,
          "description": "设定温度(°C×10)",
          "category": "temperature"
        },
        {
          "address": 400002,
          "value": 850,
          "description": "设定压力(bar×10)",
          "category": "pressure"
        }
      ]
    },
    "isLocked": false,
    "lockedBy": null,
    "lockedAt": null,
    "createdBy": "admin",
    "createdAt": "2024-01-15T08:00:00Z",
    "updatedBy": "operator1",
    "updatedAt": "2024-01-20T09:00:00Z"
  }
}
```

### 5.3 创建配方

创建新的配方。

**请求**
```http
POST /api/recipes
Content-Type: application/json
Authorization: Bearer {jwt_token}

{
  "name": "产品B标准配方",
  "description": "产品B的标准生产配方",
  "version": "1.0",
  "content": {
    "metadata": {
      "productCode": "PB001",
      "temperature": 200,
      "pressure": 90,
      "cycleTime": 50
    },
    "registers": [
      {
        "address": 400001,
        "value": 2000,
        "description": "设定温度(°C×10)",
        "category": "temperature"
      },
      {
        "address": 400002,
        "value": 900,
        "description": "设定压力(bar×10)",
        "category": "pressure"
      }
    ]
  }
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "id": 26,
    "name": "产品B标准配方",
    "description": "产品B的标准生产配方",
    "version": "1.0",
    "createdBy": "admin",
    "createdAt": "2024-01-20T10:30:00Z",
    "registerCount": 2
  },
  "message": "配方创建成功"
}
```

### 5.4 锁定配方

锁定配方进行编辑。

**请求**
```http
POST /api/recipes/1/lock
Authorization: Bearer {jwt_token}
```

**响应**
```json
{
  "success": true,
  "data": {
    "recipeId": 1,
    "isLocked": true,
    "lockedBy": "admin",
    "lockedAt": "2024-01-20T10:30:00Z",
    "lockExpiresAt": "2024-01-20T11:00:00Z"
  },
  "message": "配方锁定成功"
}
```

**错误响应（配方已被锁定）**
```json
{
  "success": false,
  "error": {
    "code": "RECIPE_LOCKED",
    "message": "配方已被其他用户锁定",
    "details": "Recipe is locked by operator1 until 2024-01-20T11:15:00Z"
  }
}
```

### 5.5 解锁配方

解锁配方。

**请求**
```http
POST /api/recipes/1/unlock
Authorization: Bearer {jwt_token}
```

**响应**
```json
{
  "success": true,
  "data": {
    "recipeId": 1,
    "isLocked": false,
    "unlockedBy": "admin",
    "unlockedAt": "2024-01-20T10:35:00Z"
  },
  "message": "配方解锁成功"
}
```

### 5.6 更新配方

更新配方内容（需要先锁定）。

**请求**
```http
PUT /api/recipes/1
Content-Type: application/json
Authorization: Bearer {jwt_token}

{
  "name": "产品A标准配方(已更新)",
  "description": "产品A的标准生产配方 - 更新版",
  "version": "1.3",
  "content": {
    "metadata": {
      "productCode": "PA001",
      "temperature": 185,
      "pressure": 88,
      "cycleTime": 47
    },
    "registers": [
      {
        "address": 400001,
        "value": 1850,
        "description": "设定温度(°C×10)",
        "category": "temperature"
      },
      {
        "address": 400002,
        "value": 880,
        "description": "设定压力(bar×10)",
        "category": "pressure"
      }
    ]
  }
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "产品A标准配方(已更新)",
    "description": "产品A的标准生产配方 - 更新版",
    "version": "1.3",
    "updatedBy": "admin",
    "updatedAt": "2024-01-20T10:40:00Z",
    "registerCount": 2
  },
  "message": "配方更新成功"
}
```

### 5.7 获取配方历史版本

获取配方的历史版本记录。

**请求**
```http
GET /api/recipes/1/history?page=1&pageSize=10
```

**响应**
```json
{
  "success": true,
  "data": {
    "recipeId": 1,
    "recipeName": "产品A标准配方",
    "history": [
      {
        "id": 15,
        "version": "1.3",
        "changeType": "UPDATE",
        "changeDescription": "更新温度和压力参数",
        "changedBy": "admin",
        "changedAt": "2024-01-20T10:40:00Z"
      },
      {
        "id": 14,
        "version": "1.2",
        "changeType": "UPDATE",
        "changeDescription": "调整循环时间",
        "changedBy": "operator1",
        "changedAt": "2024-01-20T09:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "totalItems": 5
    }
  }
}
```

### 5.8 复制配方

基于现有配方创建副本。

**请求**
```http
POST /api/recipes/1/copy
Content-Type: application/json
Authorization: Bearer {jwt_token}

{
  "name": "产品A配方副本",
  "description": "基于产品A标准配方创建的副本"
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "id": 27,
    "name": "产品A配方副本",
    "description": "基于产品A标准配方创建的副本",
    "version": "1.0",
    "sourceRecipeId": 1,
    "createdBy": "admin",
    "createdAt": "2024-01-20T10:45:00Z"
  },
  "message": "配方复制成功"
}
```

---

## 6. 任务管理接口

### 6.1 执行配方下发

启动配方下发任务。

**请求**
```http
POST /api/tasks/execute-recipe
Content-Type: application/json
Authorization: Bearer {jwt_token}

{
  "recipeId": 1,
  "deviceIds": [1, 2, 3, 4, 5],
  "description": "批量下发产品A标准配方",
  "priority": "High"
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "taskId": "550e8400-e29b-41d4-a716-446655440000",
    "recipeId": 1,
    "recipeName": "产品A标准配方",
    "deviceIds": [1, 2, 3, 4, 5],
    "totalDevices": 5,
    "status": "Running",
    "progress": 0,
    "startedAt": "2024-01-20T10:30:00Z",
    "createdBy": "admin"
  },
  "message": "配方下发任务已启动"
}
```

### 6.2 获取任务状态

获取指定任务的详细状态。

**请求**
```http
GET /api/tasks/550e8400-e29b-41d4-a716-446655440000
```

**响应**
```json
{
  "success": true,
  "data": {
    "taskId": "550e8400-e29b-41d4-a716-446655440000",
    "recipeId": 1,
    "recipeName": "产品A标准配方",
    "deviceIds": [1, 2, 3, 4, 5],
    "totalDevices": 5,
    "status": "Running",
    "progress": 65.5,
    "startedAt": "2024-01-20T10:30:00Z",
    "estimatedCompletion": "2024-01-20T10:35:00Z",
    "createdBy": "admin",
    "currentStep": "正在写入设备3...",
    "deviceResults": [
      {
        "deviceId": 1,
        "deviceName": "1号注塑机",
        "status": "Completed",
        "progress": 100,
        "registersWritten": 15,
        "completedAt": "2024-01-20T10:31:30Z"
      },
      {
        "deviceId": 2,
        "deviceName": "2号注塑机",
        "status": "Completed",
        "progress": 100,
        "registersWritten": 15,
        "completedAt": "2024-01-20T10:32:15Z"
      },
      {
        "deviceId": 3,
        "deviceName": "3号注塑机",
        "status": "Running",
        "progress": 60,
        "registersWritten": 9,
        "currentRegister": 400010
      },
      {
        "deviceId": 4,
        "deviceName": "4号注塑机",
        "status": "Pending",
        "progress": 0
      },
      {
        "deviceId": 5,
        "deviceName": "5号注塑机",
        "status": "Pending",
        "progress": 0
      }
    ]
  }
}
```

### 6.3 取消任务

取消正在执行的任务。

**请求**
```http
POST /api/tasks/550e8400-e29b-41d4-a716-446655440000/cancel
Authorization: Bearer {jwt_token}
```

**响应**
```json
{
  "success": true,
  "data": {
    "taskId": "550e8400-e29b-41d4-a716-446655440000",
    "status": "Cancelled",
    "cancelledAt": "2024-01-20T10:33:00Z",
    "cancelledBy": "admin",
    "partialResults": {
      "completedDevices": 2,
      "failedDevices": 0,
      "cancelledDevices": 3
    }
  },
  "message": "任务已取消"
}
```

### 6.4 获取任务列表

获取任务历史记录。

**请求**
```http
GET /api/tasks?page=1&pageSize=20&status=all&startDate=2024-01-15&endDate=2024-01-20
```

**查询参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | integer | 否 | 页码 |
| pageSize | integer | 否 | 每页大小 |
| status | string | 否 | 状态筛选：all/running/completed/failed/cancelled |
| startDate | date | 否 | 开始日期 |
| endDate | date | 否 | 结束日期 |
| createdBy | string | 否 | 创建者筛选 |

**响应**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "taskId": "550e8400-e29b-41d4-a716-446655440000",
        "recipeId": 1,
        "recipeName": "产品A标准配方",
        "deviceCount": 5,
        "status": "Completed",
        "progress": 100,
        "startedAt": "2024-01-20T10:30:00Z",
        "completedAt": "2024-01-20T10:35:45Z",
        "duration": "PT5M45S",
        "createdBy": "admin",
        "summary": {
          "successDevices": 5,
          "failedDevices": 0,
          "totalRegisters": 75
        }
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "totalItems": 156
    }
  }
}
```

### 6.5 重新执行失败任务

重新执行失败的设备。

**请求**
```http
POST /api/tasks/550e8400-e29b-41d4-a716-446655440000/retry
Content-Type: application/json
Authorization: Bearer {jwt_token}

{
  "deviceIds": [3, 5],  // 仅重试失败的设备
  "description": "重试失败设备"
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "newTaskId": "660e8400-e29b-41d4-a716-446655440001",
    "originalTaskId": "550e8400-e29b-41d4-a716-446655440000",
    "retryDevices": [3, 5],
    "status": "Running",
    "startedAt": "2024-01-20T10:40:00Z"
  },
  "message": "重试任务已启动"
}
```

---

## 7. 用户管理接口

### 7.1 获取用户列表

获取系统用户列表（仅管理员）。

**请求**
```http
GET /api/users?page=1&pageSize=20&role=all&status=active
Authorization: Bearer {jwt_token}
```

**查询参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | integer | 否 | 页码 |
| pageSize | integer | 否 | 每页大小 |
| role | string | 否 | 角色筛选：all/Admin/Operator/Viewer |
| status | string | 否 | 状态筛选：all/active/inactive |
| keyword | string | 否 | 搜索关键词 |

**响应**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "username": "admin",
        "fullName": "系统管理员",
        "role": "Admin",
        "isActive": true,
        "lastLoginAt": "2024-01-20T08:00:00Z",
        "createdAt": "2024-01-01T00:00:00Z"
      },
      {
        "id": 2,
        "username": "operator1",
        "fullName": "操作员1",
        "role": "Operator",
        "isActive": true,
        "lastLoginAt": "2024-01-20T09:30:00Z",
        "createdAt": "2024-01-02T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "totalItems": 8
    }
  }
}
```

### 7.2 创建用户

创建新用户（仅管理员）。

**请求**
```http
POST /api/users
Content-Type: application/json
Authorization: Bearer {jwt_token}

{
  "username": "operator3",
  "password": "Password123!",
  "fullName": "操作员3",
  "role": "Operator"
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "id": 9,
    "username": "operator3",
    "fullName": "操作员3",
    "role": "Operator",
    "isActive": true,
    "createdAt": "2024-01-20T10:30:00Z"
  },
  "message": "用户创建成功"
}
```

### 7.3 更新用户

更新用户信息。

**请求**
```http
PUT /api/users/9
Content-Type: application/json
Authorization: Bearer {jwt_token}

{
  "fullName": "操作员3(已更新)",
  "role": "Viewer",
  "isActive": true
}
```

### 7.4 删除用户

删除用户（仅管理员）。

**请求**
```http
DELETE /api/users/9
Authorization: Bearer {jwt_token}
```

### 7.5 修改密码

用户修改自己的密码。

**请求**
```http
POST /api/users/change-password
Content-Type: application/json
Authorization: Bearer {jwt_token}

{
  "currentPassword": "OldPassword123!",
  "newPassword": "NewPassword123!"
}
```

**响应**
```json
{
  "success": true,
  "message": "密码修改成功"
}
```

---

## 8. 系统管理接口

### 8.1 系统健康检查

检查系统健康状态。

**请求**
```http
GET /api/system/health
```

**响应**
```json
{
  "success": true,
  "data": {
    "status": "Healthy",
    "timestamp": "2024-01-20T10:30:00Z",
    "version": "1.0.0",
    "uptime": "PT25H30M15S",
    "services": {
      "database": {
        "status": "Healthy",
        "responseTime": "5ms",
        "details": "SQLite connection OK"
      },
      "redis": {
        "status": "Healthy",
        "responseTime": "2ms",
        "details": "Redis server responding"
      },
      "modbusPool": {
        "status": "Healthy",
        "activeConnections": 45,
        "maxConnections": 80,
        "details": "Connection pool operational"
      }
    },
    "statistics": {
      "activeDevices": 198,
      "totalDevices": 200,
      "onlineDevices": 195,
      "errorDevices": 3,
      "runningTasks": 2
    }
  }
}
```

### 8.2 获取系统统计

获取系统运行统计数据。

**请求**
```http
GET /api/system/statistics?period=24h
```

**查询参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| period | string | 否 | 统计周期：1h/24h/7d/30d，默认24h |

**响应**
```json
{
  "success": true,
  "data": {
    "period": "24h",
    "timestamp": "2024-01-20T10:30:00Z",
    "devices": {
      "total": 200,
      "online": 195,
      "offline": 2,
      "error": 3,
      "onlineRate": 97.5
    },
    "tasks": {
      "total": 25,
      "completed": 22,
      "failed": 2,
      "cancelled": 1,
      "successRate": 88.0
    },
    "performance": {
      "avgResponseTime": 45.6,
      "maxResponseTime": 120.3,
      "dataPointsCollected": 17280000,
      "errorsCount": 45
    },
    "system": {
      "cpuUsage": 15.6,
      "memoryUsage": 67.8,
      "diskUsage": 25.4,
      "networkIn": 125.6,
      "networkOut": 89.3
    }
  }
}
```

### 8.3 获取操作日志

获取系统操作日志。

**请求**
```http
GET /api/system/logs?page=1&pageSize=50&level=all&startDate=2024-01-19&endDate=2024-01-20
```

**查询参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | integer | 否 | 页码 |
| pageSize | integer | 否 | 每页大小，最大100 |
| level | string | 否 | 日志级别：all/Info/Warning/Error |
| startDate | date | 否 | 开始日期 |
| endDate | date | 否 | 结束日期 |
| operation | string | 否 | 操作类型筛选 |
| userId | integer | 否 | 用户筛选 |

**响应**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 12345,
        "timestamp": "2024-01-20T10:28:30Z",
        "level": "Information",
        "userId": 1,
        "userName": "admin",
        "operation": "Recipe.Update",
        "target": "产品A标准配方(ID:1)",
        "details": "更新配方内容，版本从1.2升级到1.3",
        "ipAddress": "************5",
        "result": "Success"
      },
      {
        "id": 12344,
        "timestamp": "2024-01-20T10:25:15Z",
        "level": "Warning",
        "userId": null,
        "userName": "System",
        "operation": "Device.ConnectionFailed",
        "target": "3号注塑机(************)",
        "details": "设备连接超时，重试3次后失败",
        "ipAddress": null,
        "result": "Failed"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 50,
      "totalItems": 2580
    }
  }
}
```

### 8.4 系统配置

获取系统配置（仅管理员）。

**请求**
```http
GET /api/system/config
Authorization: Bearer {jwt_token}
```

**响应**
```json
{
  "success": true,
  "data": {
    "modbus": {
      "maxReadConnections": 80,
      "maxWriteConnections": 20,
      "connectionTimeout": 3000,
      "readTimeout": 200,
      "writeTimeout": 5000
    },
    "system": {
      "maxDeviceCount": 200,
      "pollingInterval": 500,
      "dataRetentionDays": 30,
      "maxConcurrentTasks": 5
    },
    "security": {
      "tokenExpiryDays": 7,
      "maxLoginAttempts": 5,
      "lockoutDurationMinutes": 30
    }
  }
}
```

### 8.5 更新系统配置

更新系统配置（仅管理员）。

**请求**
```http
PUT /api/system/config
Content-Type: application/json
Authorization: Bearer {jwt_token}

{
  "modbus": {
    "maxReadConnections": 100,
    "readTimeout": 250
  },
  "system": {
    "dataRetentionDays": 45
  }
}
```

### 8.6 系统备份

创建系统数据备份。

**请求**
```http
POST /api/system/backup
Authorization: Bearer {jwt_token}

{
  "includeHistoryData": true,
  "description": "定期数据备份"
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "backupId": "backup-20240120-103000",
    "fileName": "industry-backup-20240120-103000.db",
    "size": 156780000,
    "createdAt": "2024-01-20T10:30:00Z",
    "description": "定期数据备份",
    "includesHistoryData": true
  },
  "message": "备份创建成功"
}
```

---

## 9. SignalR实时通信

### 9.1 连接Hub

客户端连接SignalR Hub以接收实时更新。

**JavaScript示例**
```javascript
const connection = new signalR.HubConnectionBuilder()
    .withUrl("http://server:5000/deviceHub", {
        accessTokenFactory: () => localStorage.getItem("jwt_token")
    })
    .withAutomaticReconnect()
    .build();

// 启动连接
await connection.start();
```

### 9.2 订阅设备更新

**客户端调用**
```javascript
// 订阅所有设备更新
connection.on("DeviceStatusUpdate", function (deviceStatus) {
    console.log("设备状态更新:", deviceStatus);
    updateDeviceUI(deviceStatus);
});

// 订阅特定设备
await connection.invoke("SubscribeToDevice", 1);
```

**服务端推送数据格式**
```json
{
  "deviceId": 1,
  "deviceName": "1号注塑机",
  "ipAddress": "************",
  "status": "online",
  "registers": [
    {
      "address": 400001,
      "value": 1250,
      "timestamp": "2024-01-20T10:29:45Z"
    }
  ],
  "lastUpdate": "2024-01-20T10:29:45Z",
  "quality": "Good"
}
```

### 9.3 任务进度更新

**客户端订阅**
```javascript
connection.on("TaskProgressUpdate", function (progress) {
    console.log("任务进度更新:", progress);
    updateTaskProgress(progress);
});
```

**推送数据格式**
```json
{
  "taskId": "550e8400-e29b-41d4-a716-446655440000",
  "progress": 65.5,
  "message": "正在写入设备3...",
  "currentDevice": {
    "deviceId": 3,
    "deviceName": "3号注塑机",
    "progress": 60
  },
  "updateTime": "2024-01-20T10:32:30Z"
}
```

### 9.4 系统通知

**客户端订阅**
```javascript
connection.on("SystemNotification", function (notification) {
    console.log("系统通知:", notification);
    showNotification(notification);
});
```

**推送数据格式**
```json
{
  "id": "notification-001",
  "type": "Warning",
  "title": "设备连接异常",
  "message": "设备"3号注塑机"连接超时，请检查网络状态",
  "deviceId": 3,
  "timestamp": "2024-01-20T10:33:00Z",
  "actions": [
    {
      "label": "查看详情",
      "action": "showDeviceDetail",
      "parameters": { "deviceId": 3 }
    }
  ]
}
```

---

## 10. 错误代码说明

### 10.1 通用错误代码

| 错误代码 | HTTP状态 | 说明 |
|----------|----------|------|
| INVALID_REQUEST | 400 | 请求参数无效 |
| UNAUTHORIZED | 401 | 未认证或Token无效 |
| FORBIDDEN | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| CONFLICT | 409 | 资源冲突 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |
| SERVICE_UNAVAILABLE | 503 | 服务不可用 |

### 10.2 业务错误代码

| 错误代码 | 说明 |
|----------|------|
| DEVICE_NOT_FOUND | 设备不存在 |
| DEVICE_OFFLINE | 设备离线 |
| DEVICE_CONNECTION_FAILED | 设备连接失败 |
| DEVICE_IP_CONFLICT | 设备IP地址冲突 |
| RECIPE_NOT_FOUND | 配方不存在 |
| RECIPE_LOCKED | 配方已被锁定 |
| RECIPE_INVALID_CONTENT | 配方内容格式错误 |
| TASK_NOT_FOUND | 任务不存在 |
| TASK_ALREADY_RUNNING | 任务已在运行 |
| TASK_CANCELLED | 任务已被取消 |
| USER_NOT_FOUND | 用户不存在 |
| USERNAME_EXISTS | 用户名已存在 |
| INVALID_PASSWORD | 密码格式错误 |
| PASSWORD_MISMATCH | 密码不匹配 |

### 10.3 错误响应示例

**设备不存在**
```json
{
  "success": false,
  "error": {
    "code": "DEVICE_NOT_FOUND",
    "message": "设备不存在",
    "details": "Device with ID 999 was not found",
    "field": "deviceId",
    "value": 999
  },
  "timestamp": "2024-01-20T10:30:45Z"
}
```

**参数验证错误**
```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "请求参数验证失败",
    "details": "Validation errors occurred",
    "validationErrors": [
      {
        "field": "name",
        "message": "设备名称不能为空"
      },
      {
        "field": "ipAddress",
        "message": "IP地址格式不正确"
      }
    ]
  },
  "timestamp": "2024-01-20T10:30:45Z"
}
```

---

## 使用示例

### JavaScript/TypeScript 客户端示例

```javascript
class IndustryMonitorAPI {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl;
        this.token = token;
    }
    
    async request(method, endpoint, data = null) {
        const url = `${this.baseUrl}/api${endpoint}`;
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
            }
        };
        
        if (data) {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(url, options);
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.error.message);
        }
        
        return result.data;
    }
    
    // 设备管理
    async getDevices(params = {}) {
        const query = new URLSearchParams(params).toString();
        return this.request('GET', `/devices?${query}`);
    }
    
    async getDevice(id) {
        return this.request('GET', `/devices/${id}`);
    }
    
    async createDevice(deviceData) {
        return this.request('POST', '/devices', deviceData);
    }
    
    // 配方管理
    async getRecipes(params = {}) {
        const query = new URLSearchParams(params).toString();
        return this.request('GET', `/recipes?${query}`);
    }
    
    async executeRecipe(recipeId, deviceIds) {
        return this.request('POST', '/tasks/execute-recipe', {
            recipeId,
            deviceIds
        });
    }
    
    // 任务监控
    async getTaskStatus(taskId) {
        return this.request('GET', `/tasks/${taskId}`);
    }
}

// 使用示例
const api = new IndustryMonitorAPI('http://*************:5000', 'your-jwt-token');

// 获取设备列表
try {
    const devices = await api.getDevices({ status: 'online' });
    console.log('在线设备:', devices);
} catch (error) {
    console.error('获取设备失败:', error.message);
}

// 执行配方下发
try {
    const task = await api.executeRecipe(1, [1, 2, 3]);
    console.log('任务已启动:', task);
    
    // 监控任务进度
    const checkProgress = async () => {
        const status = await api.getTaskStatus(task.taskId);
        console.log(`进度: ${status.progress}%`);
        
        if (status.status !== 'Completed' && status.status !== 'Failed') {
            setTimeout(checkProgress, 2000);
        }
    };
    
    checkProgress();
} catch (error) {
    console.error('执行失败:', error.message);
}
```

### C# 客户端示例

```csharp
public class IndustryMonitorClient
{
    private readonly HttpClient _httpClient;
    private readonly string _baseUrl;
    
    public IndustryMonitorClient(string baseUrl, string token)
    {
        _baseUrl = baseUrl;
        _httpClient = new HttpClient();
        _httpClient.DefaultRequestHeaders.Authorization = 
            new AuthenticationHeaderValue("Bearer", token);
        _httpClient.DefaultRequestHeaders.Accept.Add(
            new MediaTypeWithQualityHeaderValue("application/json"));
    }
    
    public async Task<ApiResponse<T>> RequestAsync<T>(string method, string endpoint, object data = null)
    {
        var url = $"{_baseUrl}/api{endpoint}";
        var request = new HttpRequestMessage(new HttpMethod(method), url);
        
        if (data != null)
        {
            var json = JsonSerializer.Serialize(data);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");
        }
        
        var response = await _httpClient.SendAsync(request);
        var content = await response.Content.ReadAsStringAsync();
        
        return JsonSerializer.Deserialize<ApiResponse<T>>(content);
    }
    
    public async Task<List<Device>> GetDevicesAsync()
    {
        var response = await RequestAsync<PagedResponse<Device>>("GET", "/devices");
        return response.Data.Items;
    }
    
    public async Task<WriteTaskDto> ExecuteRecipeAsync(int recipeId, List<int> deviceIds)
    {
        var request = new { recipeId, deviceIds };
        var response = await RequestAsync<WriteTaskDto>("POST", "/tasks/execute-recipe", request);
        return response.Data;
    }
}
```

---

本API文档涵盖了工业设备监控系统的所有接口。如有疑问或需要技术支持，请联系开发团队。