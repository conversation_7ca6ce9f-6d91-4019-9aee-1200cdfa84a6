using Microsoft.IdentityModel.Tokens;
using Microsoft.Extensions.Options;
using IndustryMonitor.Shared.Models.Configs;
using IndustryMonitor.Server.Data.Repositories;
using IndustryMonitor.Shared.Models.Entities;
using IndustryMonitor.Shared.Enums;
using IndustryMonitor.Shared.Models.DTOs;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace IndustryMonitor.Server.Services.Auth;

/// <summary>
/// 认证服务接口
/// </summary>
public interface IAuthService
{
    Task<LoginResult> LoginAsync(LoginRequest request);
    Task<RefreshTokenResult> RefreshTokenAsync(string refreshToken);
    Task LogoutAsync(string username);
    Task<UserDto> GetCurrentUserAsync(string username);
    Task<UserDto> RegisterUserAsync(CreateUserRequest request);
    Task<UserDto> UpdateUserAsync(int userId, UpdateUserRequest request);
    Task<bool> ChangePasswordAsync(int userId, ChangePasswordRequest request);
    Task<IEnumerable<UserDto>> GetAllUsersAsync();
    Task DeleteUserAsync(int userId);
    Task<bool> ValidateTokenAsync(string token);
}

/// <summary>
/// 认证服务实现
/// </summary>
public class AuthService : IAuthService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly JwtConfig _jwtConfig;
    private readonly ILogger<AuthService> _logger;
    private readonly JwtSecurityTokenHandler _tokenHandler;

    public AuthService(
        IUnitOfWork unitOfWork,
        IOptions<JwtConfig> jwtConfig,
        ILogger<AuthService> logger)
    {
        _unitOfWork = unitOfWork;
        _jwtConfig = jwtConfig.Value;
        _logger = logger;
        _tokenHandler = new JwtSecurityTokenHandler();
    }

    public async Task<LoginResult> LoginAsync(LoginRequest request)
    {
        try
        {
            // 验证用户
            var user = await _unitOfWork.Users.GetByUsernameAsync(request.Username);
            if (user == null)
            {
                await LogOperationAsync(0, OperationType.UserLogin, "登录失败：用户不存在", request.Username, request.IpAddress ?? "");
                return new LoginResult { Success = false, Message = "用户名或密码错误" };
            }

            if (!user.IsActive)
            {
                await LogOperationAsync(user.Id, OperationType.UserLogin, "登录失败：用户已禁用", request.Username, request.IpAddress ?? "");
                return new LoginResult { Success = false, Message = "用户账户已被禁用" };
            }

            // 验证密码
            if (!BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash))
            {
                await LogOperationAsync(user.Id, OperationType.UserLogin, "登录失败：密码错误", request.Username, request.IpAddress ?? "");
                return new LoginResult { Success = false, Message = "用户名或密码错误" };
            }

            // 生成JWT token
            var token = GenerateJwtToken(user);
            var refreshToken = GenerateRefreshToken();

            // 更新用户最后登录时间
            user.LastLoginAt = DateTime.UtcNow;
            user.LastLoginIp = request.IpAddress;
            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            await LogOperationAsync(user.Id, OperationType.UserLogin, "登录成功", request.Username, request.IpAddress ?? "");

            _logger.LogInformation("用户登录成功: {Username} from {IpAddress}", request.Username, request.IpAddress);

            return new LoginResult
            {
                Success = true,
                Message = "登录成功",
                Token = token,
                RefreshToken = refreshToken,
                ExpiresAt = DateTime.UtcNow.AddMinutes(_jwtConfig.ExpirationMinutes),
                User = MapToDto(user)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "登录处理失败: Username = {Username}", request.Username);
            return new LoginResult { Success = false, Message = "登录失败，请稍后重试" };
        }
    }

    public async Task<RefreshTokenResult> RefreshTokenAsync(string refreshToken)
    {
        try
        {
            // 在实际应用中，应该验证refresh token的有效性
            // 这里简化处理，直接返回失败
            await Task.CompletedTask;
            
            _logger.LogWarning("刷新token功能需要完善实现");
            return new RefreshTokenResult { Success = false, Message = "刷新token功能暂未实现" };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新token失败");
            return new RefreshTokenResult { Success = false, Message = "刷新token失败" };
        }
    }

    public async Task LogoutAsync(string username)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByUsernameAsync(username);
            if (user != null)
            {
                await LogOperationAsync(user.Id, OperationType.UserLogout, "用户退出登录", username, "");
                _logger.LogInformation("用户退出登录: {Username}", username);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理用户退出登录失败: Username = {Username}", username);
        }
    }

    public async Task<UserDto> GetCurrentUserAsync(string username)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByUsernameAsync(username);
            if (user == null)
            {
                throw new InvalidOperationException("用户不存在");
            }

            return MapToDto(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取当前用户信息失败: Username = {Username}", username);
            throw;
        }
    }

    public async Task<UserDto> RegisterUserAsync(CreateUserRequest request)
    {
        try
        {
            // 检查用户名是否已存在
            var existingUser = await _unitOfWork.Users.GetByUsernameAsync(request.Username);
            if (existingUser != null)
            {
                throw new InvalidOperationException("用户名已存在");
            }

            // 创建新用户
            var user = new User
            {
                Username = request.Username,
                PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password),
                FullName = request.FullName,
                Role = request.Role,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            var createdUser = await _unitOfWork.Users.AddAsync(user);
            await _unitOfWork.SaveChangesAsync();

            await LogOperationAsync(createdUser.Id, OperationType.UserManagement, 
                $"创建用户: {request.Username}, 角色: {request.Role}", request.Username, "");

            _logger.LogInformation("创建用户成功: {Username}, Role: {Role}", request.Username, request.Role);

            return MapToDto(createdUser);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "注册用户失败: Username = {Username}", request.Username);
            throw;
        }
    }

    public async Task<UserDto> UpdateUserAsync(int userId, UpdateUserRequest request)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null)
            {
                throw new InvalidOperationException("用户不存在");
            }

            // 检查用户名是否被其他用户使用
            if (!string.IsNullOrEmpty(request.Username) && request.Username != user.Username)
            {
                var existingUser = await _unitOfWork.Users.GetByUsernameAsync(request.Username);
                if (existingUser != null)
                {
                    throw new InvalidOperationException("用户名已被使用");
                }
            }

            // 更新用户信息
            var changes = new List<string>();
            
            if (!string.IsNullOrEmpty(request.Username) && request.Username != user.Username)
            {
                changes.Add($"用户名: {user.Username} -> {request.Username}");
                user.Username = request.Username;
            }
            
            if (!string.IsNullOrEmpty(request.FullName) && request.FullName != user.FullName)
            {
                changes.Add($"姓名: {user.FullName} -> {request.FullName}");
                user.FullName = request.FullName;
            }
            
            if (request.Role.HasValue && request.Role.Value != user.Role)
            {
                changes.Add($"角色: {user.Role} -> {request.Role.Value}");
                user.Role = request.Role.Value;
            }
            
            if (request.IsActive.HasValue && request.IsActive.Value != user.IsActive)
            {
                changes.Add($"状态: {(user.IsActive ? "激活" : "禁用")} -> {(request.IsActive.Value ? "激活" : "禁用")}");
                user.IsActive = request.IsActive.Value;
            }

            user.UpdatedAt = DateTime.UtcNow;
            
            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            if (changes.Any())
            {
                await LogOperationAsync(userId, OperationType.UserManagement, 
                    $"更新用户信息: {string.Join(", ", changes)}", user.Username, "");
            }

            _logger.LogInformation("更新用户成功: {Username}", user.Username);

            return MapToDto(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户失败: UserId = {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> ChangePasswordAsync(int userId, ChangePasswordRequest request)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null)
            {
                throw new InvalidOperationException("用户不存在");
            }

            // 验证旧密码
            if (!BCrypt.Net.BCrypt.Verify(request.OldPassword, user.PasswordHash))
            {
                return false;
            }

            // 更新密码
            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.NewPassword);
            user.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            await LogOperationAsync(userId, OperationType.UserManagement, "修改密码", user.Username, "");

            _logger.LogInformation("用户修改密码成功: {Username}", user.Username);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "修改密码失败: UserId = {UserId}", userId);
            throw;
        }
    }

    public async Task<IEnumerable<UserDto>> GetAllUsersAsync()
    {
        try
        {
            var users = await _unitOfWork.Users.GetAllAsync();
            return users.Select(MapToDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有用户失败");
            throw;
        }
    }

    public async Task DeleteUserAsync(int userId)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null)
            {
                throw new InvalidOperationException("用户不存在");
            }

            await _unitOfWork.Users.DeleteAsync(user);
            await _unitOfWork.SaveChangesAsync();

            await LogOperationAsync(userId, OperationType.UserManagement, 
                $"删除用户: {user.Username}", user.Username, "");

            _logger.LogInformation("删除用户成功: {Username}", user.Username);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除用户失败: UserId = {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> ValidateTokenAsync(string token)
    {
        try
        {
            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtConfig.Secret)),
                ValidateIssuer = true,
                ValidIssuer = _jwtConfig.Issuer,
                ValidateAudience = true,
                ValidAudience = _jwtConfig.Audience,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            var principal = _tokenHandler.ValidateToken(token, tokenValidationParameters, out _);
            return principal != null;
        }
        catch (SecurityTokenException ex)
        {
            _logger.LogWarning(ex, "Token验证失败");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Token验证过程中发生错误");
            return false;
        }
    }

    private string GenerateJwtToken(User user)
    {
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtConfig.Secret));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var claims = new[]
        {
            new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new Claim(ClaimTypes.Name, user.Username),
            new Claim(ClaimTypes.Role, user.Role.ToString()),
            new Claim("FullName", user.FullName),
            new Claim("IsActive", user.IsActive.ToString())
        };

        var token = new JwtSecurityToken(
            issuer: _jwtConfig.Issuer,
            audience: _jwtConfig.Audience,
            claims: claims,
            expires: DateTime.UtcNow.AddMinutes(_jwtConfig.ExpirationMinutes),
            signingCredentials: credentials
        );

        return _tokenHandler.WriteToken(token);
    }

    private string GenerateRefreshToken()
    {
        // 简化实现，实际应该生成更安全的refresh token
        return Guid.NewGuid().ToString();
    }

    private async Task LogOperationAsync(int userId, OperationType operation, string details, string username, string ipAddress)
    {
        try
        {
            var log = new OperationLog
            {
                UserId = userId,
                Operation = operation,
                Details = details,
                IpAddress = ipAddress,
                UserAgent = "AuthService",
                CreatedAt = DateTime.UtcNow
            };

            await _unitOfWork.OperationLogs.AddAsync(log);
            await _unitOfWork.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "记录操作日志失败: Username = {Username}", username);
        }
    }

    private static UserDto MapToDto(User user)
    {
        return new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            FullName = user.FullName,
            Role = user.Role,
            IsActive = user.IsActive,
            LastLoginAt = user.LastLoginAt,
            LastLoginIp = user.LastLoginIp,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt
        };
    }
}