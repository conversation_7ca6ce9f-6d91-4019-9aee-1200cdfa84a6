namespace IndustryMonitor.Shared.DTOs.Responses;

/// <summary>
/// API通用响应
/// </summary>
public class ApiResponse<T>
{
    public bool Success { get; set; } = true;
    public T? Data { get; set; }
    public string? Message { get; set; }
    public ErrorInfo? Error { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// API通用响应（无数据）
/// </summary>
public class ApiResponse : ApiResponse<object>
{
}

/// <summary>
/// 错误信息
/// </summary>
public class ErrorInfo
{
    public string Code { get; set; } = "";
    public string Message { get; set; } = "";
    public string? Details { get; set; }
    public string? Field { get; set; }
    public object? Value { get; set; }
    public List<ValidationError>? ValidationErrors { get; set; }
}

/// <summary>
/// 验证错误
/// </summary>
public class ValidationError
{
    public string Field { get; set; } = "";
    public string Message { get; set; } = "";
}

/// <summary>
/// 分页响应
/// </summary>
public class PagedResponse<T>
{
    public List<T> Items { get; set; } = new();
    public PaginationInfo Pagination { get; set; } = new();
}

/// <summary>
/// 分页信息
/// </summary>
public class PaginationInfo
{
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalItems { get; set; }
    public int TotalPages { get; set; }
    public bool HasNextPage { get; set; }
    public bool HasPreviousPage { get; set; }
}

/// <summary>
/// 登录响应
/// </summary>
public class LoginResponse
{
    public string Token { get; set; } = "";
    public string RefreshToken { get; set; } = "";
    public DateTime ExpiresAt { get; set; }
    public UserDto User { get; set; } = new();
}

/// <summary>
/// Token刷新响应
/// </summary>
public class RefreshTokenResponse
{
    public string Token { get; set; } = "";
    public string RefreshToken { get; set; } = "";
    public DateTime ExpiresAt { get; set; }
}

/// <summary>
/// 写入任务结果
/// </summary>
public class WriteResult
{
    public bool Success { get; set; }
    public string? TaskId { get; set; }
    public string? Message { get; set; }
    public string? Error { get; set; }
}

/// <summary>
/// 系统健康检查响应
/// </summary>
public class HealthCheckResponse
{
    public string Status { get; set; } = "";
    public DateTime Timestamp { get; set; }
    public string Version { get; set; } = "";
    public TimeSpan Uptime { get; set; }
    public Dictionary<string, ServiceHealthInfo> Services { get; set; } = new();
    public SystemStatistics? Statistics { get; set; }
}

/// <summary>
/// 服务健康信息
/// </summary>
public class ServiceHealthInfo
{
    public string Status { get; set; } = "";
    public string? ResponseTime { get; set; }
    public string? Details { get; set; }
    public int? ActiveConnections { get; set; }
    public int? MaxConnections { get; set; }
}

/// <summary>
/// 设备历史数据响应
/// </summary>
public class DeviceHistoryResponse
{
    public int DeviceId { get; set; }
    public string DeviceName { get; set; } = "";
    public TimeRangeInfo TimeRange { get; set; } = new();
    public List<DataSeries> Series { get; set; } = new();
}

/// <summary>
/// 时间范围信息
/// </summary>
public class TimeRangeInfo
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Interval { get; set; } = "";
}

/// <summary>
/// 数据序列
/// </summary>
public class DataSeries
{
    public int Register { get; set; }
    public string? Description { get; set; }
    public string? Unit { get; set; }
    public List<DataPoint> DataPoints { get; set; } = new();
}

/// <summary>
/// 数据点
/// </summary>
public class DataPoint
{
    public DateTime Timestamp { get; set; }
    public double Value { get; set; }
    public DataQuality Quality { get; set; }
}

/// <summary>
/// 配方历史响应
/// </summary>
public class RecipeHistoryResponse
{
    public int RecipeId { get; set; }
    public string RecipeName { get; set; } = "";
    public List<RecipeHistoryItem> History { get; set; } = new();
    public PaginationInfo Pagination { get; set; } = new();
}

/// <summary>
/// 配方历史项
/// </summary>
public class RecipeHistoryItem
{
    public int Id { get; set; }
    public string Version { get; set; } = "";
    public string ChangeType { get; set; } = "";
    public string? ChangeDescription { get; set; }
    public string? ChangedBy { get; set; }
    public DateTime ChangedAt { get; set; }
}

/// <summary>
/// 系统备份响应
/// </summary>
public class SystemBackupResponse
{
    public string BackupId { get; set; } = "";
    public string FileName { get; set; } = "";
    public long Size { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? Description { get; set; }
    public bool IncludesHistoryData { get; set; }
}

/// <summary>
/// 验证结果
/// </summary>
public class VerificationResult
{
    public bool Success { get; set; }
    public List<string> FailedDevices { get; set; } = new();
    public Dictionary<string, string> DeviceErrors { get; set; } = new();
}