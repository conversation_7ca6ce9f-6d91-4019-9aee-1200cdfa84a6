<UserControl x:Class="IndustryMonitor.Client.Views.DeviceListView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:IndustryMonitor.Client.Views"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid Background="{DynamicResource BackgroundBrush}">
        <Border Style="{StaticResource CustomCardStyle}" Margin="16">
            <DockPanel>
                <TextBlock DockPanel.Dock="Top" Style="{StaticResource CustomHeaderTextStyle}">设备监控</TextBlock>
                
                <ToolBarTray DockPanel.Dock="Top" IsLocked="True">
                    <ToolBar Style="{StaticResource MaterialDesignToolBar}">
                        <Button Style="{StaticResource CustomPrimaryButtonStyle}" Content="刷新" />
                        <Separator />
                        <Button Style="{StaticResource CustomButtonStyle}" Content="添加设备" />
                        <Button Style="{StaticResource CustomButtonStyle}" Content="编辑设备" />
                        <Button Style="{StaticResource CustomButtonStyle}" Content="删除设备" />
                    </ToolBar>
                </ToolBarTray>
                
                <DataGrid x:Name="DevicesDataGrid" 
                          AutoGenerateColumns="False" 
                          IsReadOnly="True"
                          ItemsSource="{Binding Devices}"
                          Style="{StaticResource MaterialDesignDataGrid}"
                          Margin="0,16,0,0">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="ID" Binding="{Binding Id}">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CustomTextStyle}">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="Name" Binding="{Binding Name}">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CustomTextStyle}">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="IP Address" Binding="{Binding IpAddress}">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CustomTextStyle}">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="Port" Binding="{Binding Port}">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CustomTextStyle}">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="Status" Binding="{Binding Status}">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CustomTextStyle}">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="Last Updated" Binding="{Binding LastUpdated}">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CustomTextStyle}">
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </DockPanel>
        </Border>
    </Grid>
</UserControl>