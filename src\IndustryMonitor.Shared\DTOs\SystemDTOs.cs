using IndustryMonitor.Shared.Enums;

namespace IndustryMonitor.Shared.DTOs;

/// <summary>
/// 设备状态 DTO
/// </summary>
public class DeviceStatusDto
{
    public int DeviceId { get; set; }
    public string DeviceName { get; set; } = "";
    public string IpAddress { get; set; } = "";
    public DeviceStatus Status { get; set; }
    public string? ErrorMessage { get; set; }
    public List<RegisterData> Registers { get; set; } = new();
    public DateTime LastUpdate { get; set; }
    public DataQuality Quality { get; set; }
}

/// <summary>
/// 寄存器数据
/// </summary>
public class RegisterData
{
    public int Address { get; set; }
    public int Value { get; set; }
    public string? Description { get; set; }
    public string? Unit { get; set; }
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 配方 DTO
/// </summary>
public class RecipeDto
{
    public int Id { get; set; }
    public string Name { get; set; } = "";
    public string? Description { get; set; }
    public string Version { get; set; } = "";
    public bool IsLocked { get; set; }
    public string? LockedBy { get; set; }
    public DateTime? LockedAt { get; set; }
    public string? CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? UpdatedBy { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int RegisterCount { get; set; }
    public RecipeContent? Content { get; set; }
}

/// <summary>
/// 配方内容
/// </summary>
public class RecipeContent
{
    public RecipeMetadata? Metadata { get; set; }
    public List<RecipeRegister> Registers { get; set; } = new();
}

/// <summary>
/// 配方元数据
/// </summary>
public class RecipeMetadata
{
    public string? ProductCode { get; set; }
    public double? Temperature { get; set; }
    public double? Pressure { get; set; }
    public int? CycleTime { get; set; }
    public Dictionary<string, object> CustomProperties { get; set; } = new();
}

/// <summary>
/// 配方寄存器
/// </summary>
public class RecipeRegister
{
    public int Address { get; set; }
    public int Value { get; set; }
    public string? Description { get; set; }
    public string? Category { get; set; }
    public string? Unit { get; set; }
}

/// <summary>
/// 写入任务 DTO
/// </summary>
public class WriteTaskDto
{
    public string TaskId { get; set; } = "";
    public int RecipeId { get; set; }
    public string RecipeName { get; set; } = "";
    public List<int> DeviceIds { get; set; } = new();
    public int TotalDevices { get; set; }
    public int TotalRegisters { get; set; }
    public WriteTaskStatus Status { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime? EstimatedCompletion { get; set; }
    public double Progress { get; set; }
    public string? CurrentStep { get; set; }
    public string CreatedBy { get; set; } = "";
    public List<DeviceWriteResult> DeviceResults { get; set; } = new();
}

/// <summary>
/// 设备写入结果
/// </summary>
public class DeviceWriteResult
{
    public int DeviceId { get; set; }
    public string DeviceName { get; set; } = "";
    public WriteTaskStatus Status { get; set; }
    public double Progress { get; set; }
    public int RegistersWritten { get; set; }
    public int? CurrentRegister { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? Error { get; set; }
}

/// <summary>
/// 用户 DTO
/// </summary>
public class UserDto
{
    public int Id { get; set; }
    public string Username { get; set; } = "";
    public string? FullName { get; set; }
    public UserRole Role { get; set; }
    public bool IsActive { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// 设备 DTO
/// </summary>
public class DeviceDto
{
    public int Id { get; set; }
    public string Name { get; set; } = "";
    public string IpAddress { get; set; } = "";
    public int Port { get; set; }
    public int SlaveId { get; set; }
    public int RegisterStart { get; set; }
    public int RegisterCount { get; set; }
    public string? DeviceType { get; set; }
    public string? Location { get; set; }
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    public DeviceStatus Status { get; set; }
    public DateTime? LastUpdate { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// 设备连接信息
/// </summary>
public class DeviceConnectionInfo
{
    public bool IsConnected { get; set; }
    public DateTime? LastConnected { get; set; }
    public TimeSpan? ConnectionDuration { get; set; }
    public int ErrorCount { get; set; }
    public string? LastError { get; set; }
}

/// <summary>
/// 系统统计信息
/// </summary>
public class SystemStatistics
{
    public int ActiveDevices { get; set; }
    public int TotalDevices { get; set; }
    public int OnlineDevices { get; set; }
    public int ErrorDevices { get; set; }
    public int RunningTasks { get; set; }
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public double DiskUsage { get; set; }
    public double NetworkIn { get; set; }
    public double NetworkOut { get; set; }
}

/// <summary>
/// 操作日志 DTO
/// </summary>
public class OperationLogDto
{
    public int Id { get; set; }
    public string? UserName { get; set; }
    public OperationType Operation { get; set; }
    public string? Target { get; set; }
    public string? Details { get; set; }
    public string? IpAddress { get; set; }
    public string Result { get; set; } = "";
    public DateTime CreatedAt { get; set; }
}