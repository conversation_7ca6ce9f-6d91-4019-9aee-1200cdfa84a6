using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace IndustryMonitor.Server.Data.Repositories;

/// <summary>
/// 通用仓储基类
/// </summary>
public class Repository<T> : IRepository<T> where T : class
{
    protected readonly AppDbContext _context;
    protected readonly DbSet<T> _dbSet;

    public Repository(AppDbContext context)
    {
        _context = context;
        _dbSet = context.Set<T>();
    }

    public virtual async Task<T?> GetByIdAsync(int id)
    {
        return await _dbSet.FindAsync(id);
    }

    public virtual async Task<T?> GetByIdAsync(string id)
    {
        return await _dbSet.FindAsync(id);
    }

    public virtual async Task<IEnumerable<T>> GetAllAsync()
    {
        return await _dbSet.ToListAsync();
    }

    public virtual async Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate)
    {
        return await _dbSet.Where(predicate).ToListAsync();
    }

    public virtual async Task<T?> FindOneAsync(Expression<Func<T, bool>> predicate)
    {
        return await _dbSet.FirstOrDefaultAsync(predicate);
    }

    public virtual async Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate)
    {
        return await _dbSet.AnyAsync(predicate);
    }

    public virtual async Task<int> CountAsync()
    {
        return await _dbSet.CountAsync();
    }

    public virtual async Task<int> CountAsync(Expression<Func<T, bool>> predicate)
    {
        return await _dbSet.CountAsync(predicate);
    }

    public virtual async Task<T> AddAsync(T entity)
    {
        var entry = await _dbSet.AddAsync(entity);
        return entry.Entity;
    }

    public virtual async Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities)
    {
        await _dbSet.AddRangeAsync(entities);
        return entities;
    }

    public virtual Task<T> UpdateAsync(T entity)
    {
        _dbSet.Update(entity);
        return Task.FromResult(entity);
    }

    public virtual Task DeleteAsync(T entity)
    {
        _dbSet.Remove(entity);
        return Task.CompletedTask;
    }

    public virtual async Task DeleteByIdAsync(int id)
    {
        var entity = await GetByIdAsync(id);
        if (entity != null)
        {
            await DeleteAsync(entity);
        }
    }

    public virtual Task DeleteRangeAsync(IEnumerable<T> entities)
    {
        _dbSet.RemoveRange(entities);
        return Task.CompletedTask;
    }

    public virtual async Task<(IEnumerable<T> Items, int TotalCount)> GetPagedAsync(
        int page, 
        int pageSize, 
        Expression<Func<T, bool>>? predicate = null,
        Expression<Func<T, object>>? orderBy = null,
        bool ascending = true)
    {
        var query = _dbSet.AsQueryable();

        if (predicate != null)
        {
            query = query.Where(predicate);
        }

        var totalCount = await query.CountAsync();

        if (orderBy != null)
        {
            query = ascending ? query.OrderBy(orderBy) : query.OrderByDescending(orderBy);
        }

        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return (items, totalCount);
    }
}

/// <summary>
/// 设备仓储实现
/// </summary>
public class DeviceRepository : Repository<Device>, IDeviceRepository
{
    public DeviceRepository(AppDbContext context) : base(context) { }

    public async Task<IEnumerable<Device>> GetActiveDevicesAsync()
    {
        return await _dbSet.Where(d => d.IsActive).ToListAsync();
    }

    public async Task<Device?> GetByIpAddressAsync(string ipAddress)
    {
        return await _dbSet.FirstOrDefaultAsync(d => d.IpAddress == ipAddress);
    }

    public async Task<IEnumerable<Device>> GetDevicesByLocationAsync(string location)
    {
        return await _dbSet.Where(d => d.Location == location).ToListAsync();
    }

    public async Task<IEnumerable<Device>> GetDevicesByTypeAsync(string deviceType)
    {
        return await _dbSet.Where(d => d.DeviceType == deviceType).ToListAsync();
    }

    public async Task<bool> IsIpAddressExistsAsync(string ipAddress, int? excludeId = null)
    {
        var query = _dbSet.Where(d => d.IpAddress == ipAddress);
        if (excludeId.HasValue)
        {
            query = query.Where(d => d.Id != excludeId.Value);
        }
        return await query.AnyAsync();
    }
}

/// <summary>
/// 配方仓储实现
/// </summary>
public class RecipeRepository : Repository<Recipe>, IRecipeRepository
{
    public RecipeRepository(AppDbContext context) : base(context) { }

    public async Task<IEnumerable<Recipe>> GetUnlockedRecipesAsync()
    {
        return await _dbSet.Where(r => !r.IsLocked).ToListAsync();
    }

    public async Task<Recipe?> GetWithHistoryAsync(int id)
    {
        return await _dbSet
            .Include(r => r.RecipeHistories)
            .FirstOrDefaultAsync(r => r.Id == id);
    }

    public async Task<bool> IsRecipeLockedAsync(int id)
    {
        var recipe = await _dbSet.FindAsync(id);
        return recipe?.IsLocked ?? false;
    }

    public async Task<Recipe?> LockRecipeAsync(int id, string lockedBy)
    {
        var recipe = await _dbSet.FindAsync(id);
        if (recipe != null && !recipe.IsLocked)
        {
            recipe.IsLocked = true;
            recipe.LockedBy = lockedBy;
            recipe.LockedAt = DateTime.UtcNow;
            return recipe;
        }
        return null;
    }

    public async Task<bool> UnlockRecipeAsync(int id, string unlockedBy)
    {
        var recipe = await _dbSet.FindAsync(id);
        if (recipe != null && recipe.IsLocked)
        {
            recipe.IsLocked = false;
            recipe.LockedBy = null;
            recipe.LockedAt = null;
            recipe.UpdatedBy = unlockedBy;
            return true;
        }
        return false;
    }

    public async Task<IEnumerable<Recipe>> GetRecipesByCreatorAsync(string createdBy)
    {
        return await _dbSet.Where(r => r.CreatedBy == createdBy).ToListAsync();
    }
}

/// <summary>
/// 用户仓储实现
/// </summary>
public class UserRepository : Repository<User>, IUserRepository
{
    public UserRepository(AppDbContext context) : base(context) { }

    public async Task<User?> GetByUsernameAsync(string username)
    {
        return await _dbSet.FirstOrDefaultAsync(u => u.Username == username);
    }

    public async Task<bool> IsUsernameExistsAsync(string username, int? excludeId = null)
    {
        var query = _dbSet.Where(u => u.Username == username);
        if (excludeId.HasValue)
        {
            query = query.Where(u => u.Id != excludeId.Value);
        }
        return await query.AnyAsync();
    }

    public async Task<IEnumerable<User>> GetActiveUsersAsync()
    {
        return await _dbSet.Where(u => u.IsActive).ToListAsync();
    }

    public async Task<IEnumerable<User>> GetUsersByRoleAsync(UserRole role)
    {
        return await _dbSet.Where(u => u.Role == role).ToListAsync();
    }
}

/// <summary>
/// 写入任务仓储实现
/// </summary>
public class WriteTaskRepository : Repository<WriteTask>, IWriteTaskRepository
{
    public WriteTaskRepository(AppDbContext context) : base(context) { }

    public async Task<WriteTask?> GetByTaskIdAsync(string taskId)
    {
        return await _dbSet
            .Include(t => t.Recipe)
            .FirstOrDefaultAsync(t => t.TaskId == taskId);
    }

    public async Task<IEnumerable<WriteTask>> GetRunningTasksAsync()
    {
        return await _dbSet
            .Include(t => t.Recipe)
            .Where(t => t.Status == WriteTaskStatus.Running || t.Status == WriteTaskStatus.Pending)
            .ToListAsync();
    }

    public async Task<IEnumerable<WriteTask>> GetTasksByStatusAsync(WriteTaskStatus status)
    {
        return await _dbSet
            .Include(t => t.Recipe)
            .Where(t => t.Status == status)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<IEnumerable<WriteTask>> GetTasksByUserAsync(string username)
    {
        return await _dbSet
            .Include(t => t.Recipe)
            .Where(t => t.CreatedBy == username)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<IEnumerable<WriteTask>> GetRecentTasksAsync(int count = 50)
    {
        return await _dbSet
            .Include(t => t.Recipe)
            .OrderByDescending(t => t.CreatedAt)
            .Take(count)
            .ToListAsync();
    }
}

/// <summary>
/// 操作日志仓储实现
/// </summary>
public class OperationLogRepository : Repository<OperationLog>, IOperationLogRepository
{
    public OperationLogRepository(AppDbContext context) : base(context) { }

    public async Task<IEnumerable<OperationLog>> GetLogsByUserAsync(int userId)
    {
        return await _dbSet
            .Where(l => l.UserId == userId)
            .OrderByDescending(l => l.CreatedAt)
            .ToListAsync();
    }

    public async Task<IEnumerable<OperationLog>> GetLogsByOperationAsync(OperationType operation)
    {
        return await _dbSet
            .Where(l => l.Operation == operation)
            .OrderByDescending(l => l.CreatedAt)
            .ToListAsync();
    }

    public async Task<IEnumerable<OperationLog>> GetLogsByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        return await _dbSet
            .Where(l => l.CreatedAt >= startDate && l.CreatedAt <= endDate)
            .OrderByDescending(l => l.CreatedAt)
            .ToListAsync();
    }

    public async Task<IEnumerable<OperationLog>> GetRecentLogsAsync(int count = 100)
    {
        return await _dbSet
            .OrderByDescending(l => l.CreatedAt)
            .Take(count)
            .ToListAsync();
    }
}

/// <summary>
/// 设备历史数据仓储实现
/// </summary>
public class DeviceHistoryRepository : Repository<DeviceHistory>, IDeviceHistoryRepository
{
    public DeviceHistoryRepository(AppDbContext context) : base(context) { }

    public async Task<IEnumerable<DeviceHistory>> GetDeviceHistoryAsync(int deviceId, DateTime startTime, DateTime endTime)
    {
        return await _dbSet
            .Where(h => h.DeviceId == deviceId && h.Timestamp >= startTime && h.Timestamp <= endTime)
            .OrderBy(h => h.Timestamp)
            .ToListAsync();
    }

    public async Task<DeviceHistory?> GetLatestDataAsync(int deviceId)
    {
        return await _dbSet
            .Where(h => h.DeviceId == deviceId)
            .OrderByDescending(h => h.Timestamp)
            .FirstOrDefaultAsync();
    }

    public async Task DeleteOldDataAsync(DateTime cutoffDate)
    {
        var oldData = await _dbSet
            .Where(h => h.Timestamp < cutoffDate)
            .ToListAsync();

        if (oldData.Any())
        {
            _dbSet.RemoveRange(oldData);
        }
    }

    public async Task<IEnumerable<DeviceHistory>> GetAggregatedDataAsync(
        int deviceId, 
        DateTime startTime, 
        DateTime endTime, 
        TimeSpan interval)
    {
        // 简化的聚合实现，实际生产环境可能需要更复杂的聚合逻辑
        var data = await _dbSet
            .Where(h => h.DeviceId == deviceId && h.Timestamp >= startTime && h.Timestamp <= endTime)
            .OrderBy(h => h.Timestamp)
            .ToListAsync();

        return data;
    }
}

/// <summary>
/// 系统配置仓储实现
/// </summary>
public class SystemConfigRepository : Repository<SystemConfig>, ISystemConfigRepository
{
    public SystemConfigRepository(AppDbContext context) : base(context) { }

    public async Task<SystemConfig?> GetByKeyAsync(string key)
    {
        return await _dbSet.FirstOrDefaultAsync(c => c.Key == key);
    }

    public async Task<string?> GetValueAsync(string key)
    {
        var config = await GetByKeyAsync(key);
        return config?.Value;
    }

    public async Task<bool> SetValueAsync(string key, string value, string? updatedBy = null)
    {
        var config = await GetByKeyAsync(key);
        if (config != null)
        {
            config.Value = value;
            config.UpdatedBy = updatedBy;
            config.UpdatedAt = DateTime.UtcNow;
        }
        else
        {
            config = new SystemConfig
            {
                Key = key,
                Value = value,
                UpdatedBy = updatedBy,
                UpdatedAt = DateTime.UtcNow
            };
            await _dbSet.AddAsync(config);
        }
        return true;
    }

    public async Task<Dictionary<string, string>> GetAllConfigsAsync()
    {
        var configs = await _dbSet.ToListAsync();
        return configs.ToDictionary(c => c.Key, c => c.Value ?? "");
    }
}