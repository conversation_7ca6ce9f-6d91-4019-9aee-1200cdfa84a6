namespace IndustryMonitor.Shared.Enums;

/// <summary>
/// 用户角色枚举
/// </summary>
public enum UserRole
{
    /// <summary>
    /// 查看者 - 仅查看权限
    /// </summary>
    Viewer = 0,
    
    /// <summary>
    /// 操作员 - 可操作设备和配方
    /// </summary>
    Operator = 1,
    
    /// <summary>
    /// 管理员 - 所有权限
    /// </summary>
    Admin = 2
}

/// <summary>
/// 设备状态枚举
/// </summary>
public enum DeviceStatus
{
    /// <summary>
    /// 离线
    /// </summary>
    Offline = 0,
    
    /// <summary>
    /// 在线
    /// </summary>
    Online = 1,
    
    /// <summary>
    /// 错误
    /// </summary>
    Error = 2,
    
    /// <summary>
    /// 正在写入
    /// </summary>
    Writing = 3
}

/// <summary>
/// 数据质量枚举
/// </summary>
public enum DataQuality
{
    /// <summary>
    /// 良好
    /// </summary>
    Good = 0,
    
    /// <summary>
    /// 不确定
    /// </summary>
    Uncertain = 1,
    
    /// <summary>
    /// 坏数据
    /// </summary>
    Bad = 2
}

/// <summary>
/// 写入任务状态枚举
/// </summary>
public enum WriteTaskStatus
{
    /// <summary>
    /// 等待中
    /// </summary>
    Pending = 0,
    
    /// <summary>
    /// 运行中
    /// </summary>
    Running = 1,
    
    /// <summary>
    /// 已完成
    /// </summary>
    Completed = 2,
    
    /// <summary>
    /// 失败
    /// </summary>
    Failed = 3,
    
    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled = 4,
    
    /// <summary>
    /// 部分成功
    /// </summary>
    PartialSuccess = 5
}

/// <summary>
/// 操作类型枚举
/// </summary>
public enum OperationType
{
    /// <summary>
    /// 用户登录
    /// </summary>
    Login,
    
    /// <summary>
    /// 用户登出
    /// </summary>
    Logout,
    
    /// <summary>
    /// 创建设备
    /// </summary>
    DeviceCreate,
    
    /// <summary>
    /// 更新设备
    /// </summary>
    DeviceUpdate,
    
    /// <summary>
    /// 删除设备
    /// </summary>
    DeviceDelete,
    
    /// <summary>
    /// 创建配方
    /// </summary>
    RecipeCreate,
    
    /// <summary>
    /// 更新配方
    /// </summary>
    RecipeUpdate,
    
    /// <summary>
    /// 删除配方
    /// </summary>
    RecipeDelete,
    
    /// <summary>
    /// 锁定配方
    /// </summary>
    RecipeLock,
    
    /// <summary>
    /// 解锁配方
    /// </summary>
    RecipeUnlock,
    
    /// <summary>
    /// 执行配方
    /// </summary>
    RecipeExecute,
    
    /// <summary>
    /// 取消任务
    /// </summary>
    TaskCancel,
    
    /// <summary>
    /// 系统配置
    /// </summary>
    SystemConfig,
    
    /// <summary>
    /// 用户管理
    /// </summary>
    UserManagement
}