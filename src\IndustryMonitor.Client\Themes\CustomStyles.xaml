<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    
    <!-- 自定义卡片样式 -->
    <Style x:Key="CustomCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="{DynamicResource CardCornerRadius}"/>
        <Setter Property="Effect" Value="{DynamicResource CardShadowEffect}"/>
        <Setter Property="Padding" Value="16"/>
    </Style>
    
    <!-- 自定义标题文本样式 -->
    <Style x:Key="CustomHeaderTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="{DynamicResource FontSizeHeader}"/>
        <Setter Property="FontWeight" Value="{DynamicResource FontWeightBold}"/>
        <Setter Property="FontFamily" Value="{DynamicResource HeaderFontFamily}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
    </Style>
    
    <!-- 自定义普通文本样式 -->
    <Style x:Key="CustomTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="{DynamicResource FontSizeNormal}"/>
        <Setter Property="FontWeight" Value="{DynamicResource FontWeightNormal}"/>
        <Setter Property="FontFamily" Value="{DynamicResource DefaultFontFamily}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
    </Style>
    
    <!-- 自定义次级文本样式 -->
    <Style x:Key="CustomSecondaryTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="{DynamicResource FontSizeSmall}"/>
        <Setter Property="FontWeight" Value="{DynamicResource FontWeightNormal}"/>
        <Setter Property="FontFamily" Value="{DynamicResource DefaultFontFamily}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextSecondaryBrush}"/>
    </Style>
    
    <!-- 自定义按钮样式 -->
    <Style x:Key="CustomButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="{DynamicResource DefaultCornerRadius}"/>
    </Style>
    
    <!-- 自定义主按钮样式 -->
    <Style x:Key="CustomPrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{DynamicResource PrimaryHueMidBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidForegroundBrush}"/>
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="{DynamicResource DefaultCornerRadius}"/>
    </Style>
    
</ResourceDictionary>