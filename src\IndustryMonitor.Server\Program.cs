using IndustryMonitor.Server.Extensions;
using IndustryMonitor.Server.Data;
using IndustryMonitor.Shared.Models.Entities;
using IndustryMonitor.Shared.Enums;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace IndustryMonitor.Server;

public class Program
{
    public static async Task Main(string[] args)
    {
        // 配置 Serilog
        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(GetConfiguration(args))
            .CreateLogger();

        try
        {
            Log.Information("工业设备监控系统服务端启动中...");
            
            var builder = WebApplication.CreateBuilder(args);
            
            // 使用 Serilog 作为日志提供程序
            builder.Host.UseSerilog();
            
            // Windows 服务支持
            if (OperatingSystem.IsWindows())
            {
                builder.Services.AddWindowsService();
            }

            // 配置服务
            builder.Services.AddIndustryMonitorServices(builder.Configuration);

            var app = builder.Build();

            // 配置 HTTP 管道
            app.UseIndustryMonitorMiddleware();

            // 数据库初始化
            await InitializeDatabaseAsync(app);

            Log.Information("工业设备监控系统服务端启动完成，监听端口: {Urls}", 
                builder.Configuration.GetSection("Kestrel:Endpoints:Http:Url").Value);

            await app.RunAsync();
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "工业设备监控系统服务端启动失败");
            throw;
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static IConfiguration GetConfiguration(string[] args)
    {
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", 
                optional: true, reloadOnChange: true)
            .AddEnvironmentVariables()
            .AddCommandLine(args)
            .Build();

        return configuration;
    }

    private static async Task InitializeDatabaseAsync(WebApplication app)
    {
        using var scope = app.Services.CreateScope();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
        
        try
        {
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            
            // 确保数据库存在
            await context.Database.EnsureCreatedAsync();
            
            // 运行数据库迁移
            // await context.Database.MigrateAsync();
            
            // 初始化种子数据
            await SeedDataAsync(context, logger);
            
            logger.LogInformation("数据库初始化完成");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "数据库初始化失败");
            throw;
        }
    }

    private static async Task SeedDataAsync(AppDbContext context, ILogger logger)
    {
        try
        {
            // 检查是否已有数据
            if (await context.Users.AnyAsync())
            {
                logger.LogInformation("数据库已有数据，跳过种子数据初始化");
                return;
            }

            // 创建默认管理员用户
            var adminUser = new User
            {
                Username = "admin",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                FullName = "系统管理员",
                Role = UserRole.Admin,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            context.Users.Add(adminUser);

            // 添加示例设备
            var devices = new[]
            {
                new Device { Name = "1号注塑机", IpAddress = "************", RegisterStart = 400001, RegisterCount = 10, DeviceType = "PLC", Location = "车间A-01", IsActive = true },
                new Device { Name = "2号注塑机", IpAddress = "************", RegisterStart = 400001, RegisterCount = 10, DeviceType = "PLC", Location = "车间A-02", IsActive = true },
                new Device { Name = "3号注塑机", IpAddress = "************", RegisterStart = 400001, RegisterCount = 10, DeviceType = "PLC", Location = "车间A-03", IsActive = true },
                new Device { Name = "4号注塑机", IpAddress = "************", RegisterStart = 400001, RegisterCount = 10, DeviceType = "PLC", Location = "车间A-04", IsActive = true },
                new Device { Name = "5号注塑机", IpAddress = "************", RegisterStart = 400001, RegisterCount = 10, DeviceType = "PLC", Location = "车间A-05", IsActive = true }
            };

            context.Devices.AddRange(devices);

            // 添加示例配方
            var recipes = new[]
            {
                new Recipe 
                { 
                    Name = "产品A标准配方", 
                    Description = "产品A的标准生产配方", 
                    Content = """{"metadata":{"productCode":"PA001","temperature":180,"pressure":85},"registers":[{"address":400001,"value":1800,"description":"设定温度(°C×10)","category":"temperature"},{"address":400002,"value":850,"description":"设定压力(bar×10)","category":"pressure"}]}""",
                    Version = "1.0", 
                    CreatedBy = "admin",
                    CreatedAt = DateTime.UtcNow
                },
                new Recipe 
                { 
                    Name = "产品B标准配方", 
                    Description = "产品B的标准生产配方", 
                    Content = """{"metadata":{"productCode":"PB001","temperature":200,"pressure":90},"registers":[{"address":400001,"value":2000,"description":"设定温度(°C×10)","category":"temperature"},{"address":400002,"value":900,"description":"设定压力(bar×10)","category":"pressure"}]}""",
                    Version = "1.0", 
                    CreatedBy = "admin",
                    CreatedAt = DateTime.UtcNow
                }
            };

            context.Recipes.AddRange(recipes);

            await context.SaveChangesAsync();
            
            logger.LogInformation("种子数据初始化完成");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "种子数据初始化失败");
            throw;
        }
    }
}