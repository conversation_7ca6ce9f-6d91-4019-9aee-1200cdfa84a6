using HslCommunication;
using HslCommunication.ModBus;
using Microsoft.Extensions.Options;
using IndustryMonitor.Shared.Models.Configs;

namespace IndustryMonitor.Server.Services.Device;

/// <summary>
/// Modbus TCP 连接实现
/// </summary>
public class ModbusConnection : IModbusConnection
{
    private readonly ModbusTcpNet _modbusTcp;
    private readonly ModbusConfig _config;
    private readonly ILogger<ModbusConnection> _logger;
    private bool _disposed = false;

    public string IpAddress { get; }
    public int Port { get; }
    public bool IsConnected => _modbusTcp.IsConnected();
    public DateTime CreatedAt { get; }
    public DateTime LastUsed { get; set; }

    public ModbusConnection(string ipAddress, int port, IOptions<ModbusConfig> config, ILogger<ModbusConnection> logger)
    {
        IpAddress = ipAddress;
        Port = port;
        _config = config.Value;
        _logger = logger;
        CreatedAt = DateTime.UtcNow;
        LastUsed = DateTime.UtcNow;

        _modbusTcp = new ModbusTcpNet(ipAddress, port, 1)
        {
            ConnectTimeOut = _config.ConnectTimeout,
            ReceiveTimeOut = _config.ReadTimeout
        };
    }

    public async Task<bool> ConnectAsync(int timeoutMs = 3000)
    {
        try
        {
            var originalTimeout = _modbusTcp.ConnectTimeOut;
            _modbusTcp.ConnectTimeOut = timeoutMs;

            var result = await Task.Run(() => _modbusTcp.ConnectServer());
            
            _modbusTcp.ConnectTimeOut = originalTimeout;
            LastUsed = DateTime.UtcNow;

            if (result.IsSuccess)
            {
                _logger.LogDebug("Modbus connection established to {IpAddress}:{Port}", IpAddress, Port);
                return true;
            }
            else
            {
                _logger.LogWarning("Failed to connect to Modbus device {IpAddress}:{Port}: {Error}", 
                    IpAddress, Port, result.Message);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception while connecting to Modbus device {IpAddress}:{Port}", IpAddress, Port);
            return false;
        }
    }

    public async Task DisconnectAsync()
    {
        try
        {
            await Task.Run(() => _modbusTcp.ConnectClose());
            _logger.LogDebug("Modbus connection closed for {IpAddress}:{Port}", IpAddress, Port);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error closing Modbus connection {IpAddress}:{Port}", IpAddress, Port);
        }
    }

    public async Task<ushort[]> ReadHoldingRegistersAsync(byte slaveId, int startAddress, int count)
    {
        try
        {
            LastUsed = DateTime.UtcNow;
            _modbusTcp.Station = slaveId;
            
            var result = await Task.Run(() => _modbusTcp.ReadUInt16(startAddress.ToString(), (ushort)count));
            
            if (result.IsSuccess)
            {
                _logger.LogTrace("Read {Count} holding registers from {SlaveId}:{Address}", count, slaveId, startAddress);
                return result.Content;
            }
            else
            {
                _logger.LogWarning("Failed to read holding registers from {SlaveId}:{Address}: {Error}", 
                    slaveId, startAddress, result.Message);
                throw new InvalidOperationException($"Failed to read holding registers: {result.Message}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception reading holding registers from {SlaveId}:{Address}", slaveId, startAddress);
            throw;
        }
    }

    public async Task<bool[]> ReadCoilsAsync(byte slaveId, int startAddress, int count)
    {
        try
        {
            LastUsed = DateTime.UtcNow;
            _modbusTcp.Station = slaveId;
            
            var result = await Task.Run(() => _modbusTcp.ReadBool(startAddress.ToString(), (ushort)count));
            
            if (result.IsSuccess)
            {
                _logger.LogTrace("Read {Count} coils from {SlaveId}:{Address}", count, slaveId, startAddress);
                return result.Content;
            }
            else
            {
                _logger.LogWarning("Failed to read coils from {SlaveId}:{Address}: {Error}", 
                    slaveId, startAddress, result.Message);
                throw new InvalidOperationException($"Failed to read coils: {result.Message}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception reading coils from {SlaveId}:{Address}", slaveId, startAddress);
            throw;
        }
    }

    public async Task<bool[]> ReadDiscreteInputsAsync(byte slaveId, int startAddress, int count)
    {
        try
        {
            LastUsed = DateTime.UtcNow;
            _modbusTcp.Station = slaveId;
            
            var result = await Task.Run(() => _modbusTcp.ReadDiscrete(startAddress.ToString(), (ushort)count));
            
            if (result.IsSuccess)
            {
                _logger.LogTrace("Read {Count} discrete inputs from {SlaveId}:{Address}", count, slaveId, startAddress);
                return result.Content;
            }
            else
            {
                _logger.LogWarning("Failed to read discrete inputs from {SlaveId}:{Address}: {Error}", 
                    slaveId, startAddress, result.Message);
                throw new InvalidOperationException($"Failed to read discrete inputs: {result.Message}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception reading discrete inputs from {SlaveId}:{Address}", slaveId, startAddress);
            throw;
        }
    }

    public async Task<ushort[]> ReadInputRegistersAsync(byte slaveId, int startAddress, int count)
    {
        try
        {
            LastUsed = DateTime.UtcNow;
            _modbusTcp.Station = slaveId;
            
            var result = await Task.Run(() => _modbusTcp.ReadInputRegister(startAddress.ToString(), (ushort)count));
            
            if (result.IsSuccess)
            {
                _logger.LogTrace("Read {Count} input registers from {SlaveId}:{Address}", count, slaveId, startAddress);
                return result.Content;
            }
            else
            {
                _logger.LogWarning("Failed to read input registers from {SlaveId}:{Address}: {Error}", 
                    slaveId, startAddress, result.Message);
                throw new InvalidOperationException($"Failed to read input registers: {result.Message}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception reading input registers from {SlaveId}:{Address}", slaveId, startAddress);
            throw;
        }
    }

    public async Task WriteMultipleRegistersAsync(byte slaveId, int startAddress, ushort[] values)
    {
        try
        {
            LastUsed = DateTime.UtcNow;
            _modbusTcp.Station = slaveId;
            
            var result = await Task.Run(() => _modbusTcp.Write(startAddress.ToString(), values));
            
            if (result.IsSuccess)
            {
                _logger.LogTrace("Wrote {Count} registers to {SlaveId}:{Address}", values.Length, slaveId, startAddress);
            }
            else
            {
                _logger.LogWarning("Failed to write registers to {SlaveId}:{Address}: {Error}", 
                    slaveId, startAddress, result.Message);
                throw new InvalidOperationException($"Failed to write registers: {result.Message}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception writing registers to {SlaveId}:{Address}", slaveId, startAddress);
            throw;
        }
    }

    public async Task WriteSingleCoilAsync(byte slaveId, int address, bool value)
    {
        try
        {
            LastUsed = DateTime.UtcNow;
            _modbusTcp.Station = slaveId;
            
            var result = await Task.Run(() => _modbusTcp.Write(address.ToString(), value));
            
            if (result.IsSuccess)
            {
                _logger.LogTrace("Wrote coil to {SlaveId}:{Address} = {Value}", slaveId, address, value);
            }
            else
            {
                _logger.LogWarning("Failed to write coil to {SlaveId}:{Address}: {Error}", 
                    slaveId, address, result.Message);
                throw new InvalidOperationException($"Failed to write coil: {result.Message}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception writing coil to {SlaveId}:{Address}", slaveId, address);
            throw;
        }
    }

    public async Task WriteMultipleCoilsAsync(byte slaveId, int startAddress, bool[] values)
    {
        try
        {
            LastUsed = DateTime.UtcNow;
            _modbusTcp.Station = slaveId;
            
            var result = await Task.Run(() => _modbusTcp.Write(startAddress.ToString(), values));
            
            if (result.IsSuccess)
            {
                _logger.LogTrace("Wrote {Count} coils to {SlaveId}:{Address}", values.Length, slaveId, startAddress);
            }
            else
            {
                _logger.LogWarning("Failed to write coils to {SlaveId}:{Address}: {Error}", 
                    slaveId, startAddress, result.Message);
                throw new InvalidOperationException($"Failed to write coils: {result.Message}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception writing coils to {SlaveId}:{Address}", slaveId, startAddress);
            throw;
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            try
            {
                _modbusTcp?.ConnectClose();
                _modbusTcp?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disposing Modbus connection {IpAddress}:{Port}", IpAddress, Port);
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}