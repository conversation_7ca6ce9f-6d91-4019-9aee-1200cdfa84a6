namespace IndustryMonitor.Server.Services.Device;

/// <summary>
/// Modbus 连接接口
/// </summary>
public interface IModbusConnection : IDisposable
{
    string IpAddress { get; }
    int Port { get; }
    bool IsConnected { get; }
    DateTime CreatedAt { get; }
    DateTime LastUsed { get; set; }
    
    Task<bool> ConnectAsync(int timeoutMs = 3000);
    Task DisconnectAsync();
    Task<ushort[]> ReadHoldingRegistersAsync(byte slaveId, int startAddress, int count);
    Task<bool[]> ReadCoilsAsync(byte slaveId, int startAddress, int count);
    Task<bool[]> ReadDiscreteInputsAsync(byte slaveId, int startAddress, int count);
    Task<ushort[]> ReadInputRegistersAsync(byte slaveId, int startAddress, int count);
    Task WriteMultipleRegistersAsync(byte slaveId, int startAddress, ushort[] values);
    Task WriteSingleCoilAsync(byte slaveId, int address, bool value);
    Task WriteMultipleCoilsAsync(byte slaveId, int startAddress, bool[] values);
}

/// <summary>
/// 连接池接口
/// </summary>
public interface IConnectionPool : IDisposable
{
    Task<IModbusConnection> GetReadConnectionAsync(string ipAddress);
    Task<IModbusConnection> GetWriteConnectionAsync(string ipAddress);
    void ReturnConnection(IModbusConnection connection);
    Task<ConnectionPoolStatistics> GetStatisticsAsync();
    Task CleanupIdleConnectionsAsync();
}

/// <summary>
/// 连接池统计信息
/// </summary>
public class ConnectionPoolStatistics
{
    public int TotalConnections { get; set; }
    public int ReadConnections { get; set; }
    public int WriteConnections { get; set; }
    public int ActiveConnections { get; set; }
    public int IdleConnections { get; set; }
    public Dictionary<string, int> ConnectionsByIp { get; set; } = new();
}

/// <summary>
/// 池化连接
/// </summary>
public class PooledConnection
{
    public string Id { get; set; } = "";
    public string IpAddress { get; set; } = "";
    public IModbusConnection Connection { get; set; } = null!;
    public DateTime CreatedAt { get; set; }
    public bool InUse { get; set; }
    public string PoolType { get; set; } = ""; // Read/Write
    public bool IsAlive => Connection?.IsConnected ?? false;
}

/// <summary>
/// 池化连接包装器
/// </summary>
public class PooledConnectionWrapper : IModbusConnection
{
    private readonly PooledConnection _pooledConnection;
    private readonly IConnectionPool _pool;
    private bool _disposed = false;

    public PooledConnectionWrapper(PooledConnection pooledConnection, IConnectionPool pool)
    {
        _pooledConnection = pooledConnection;
        _pool = pool;
        _pooledConnection.Connection.LastUsed = DateTime.UtcNow;
    }

    public string IpAddress => _pooledConnection.Connection.IpAddress;
    public int Port => _pooledConnection.Connection.Port;
    public bool IsConnected => _pooledConnection.Connection.IsConnected;
    public DateTime CreatedAt => _pooledConnection.Connection.CreatedAt;
    public DateTime LastUsed 
    { 
        get => _pooledConnection.Connection.LastUsed; 
        set => _pooledConnection.Connection.LastUsed = value; 
    }

    public Task<bool> ConnectAsync(int timeoutMs = 3000)
    {
        LastUsed = DateTime.UtcNow;
        return _pooledConnection.Connection.ConnectAsync(timeoutMs);
    }

    public Task DisconnectAsync()
    {
        return _pooledConnection.Connection.DisconnectAsync();
    }

    public Task<ushort[]> ReadHoldingRegistersAsync(byte slaveId, int startAddress, int count)
    {
        LastUsed = DateTime.UtcNow;
        return _pooledConnection.Connection.ReadHoldingRegistersAsync(slaveId, startAddress, count);
    }

    public Task<bool[]> ReadCoilsAsync(byte slaveId, int startAddress, int count)
    {
        LastUsed = DateTime.UtcNow;
        return _pooledConnection.Connection.ReadCoilsAsync(slaveId, startAddress, count);
    }

    public Task<bool[]> ReadDiscreteInputsAsync(byte slaveId, int startAddress, int count)
    {
        LastUsed = DateTime.UtcNow;
        return _pooledConnection.Connection.ReadDiscreteInputsAsync(slaveId, startAddress, count);
    }

    public Task<ushort[]> ReadInputRegistersAsync(byte slaveId, int startAddress, int count)
    {
        LastUsed = DateTime.UtcNow;
        return _pooledConnection.Connection.ReadInputRegistersAsync(slaveId, startAddress, count);
    }

    public Task WriteMultipleRegistersAsync(byte slaveId, int startAddress, ushort[] values)
    {
        LastUsed = DateTime.UtcNow;
        return _pooledConnection.Connection.WriteMultipleRegistersAsync(slaveId, startAddress, values);
    }

    public Task WriteSingleCoilAsync(byte slaveId, int address, bool value)
    {
        LastUsed = DateTime.UtcNow;
        return _pooledConnection.Connection.WriteSingleCoilAsync(slaveId, address, value);
    }

    public Task WriteMultipleCoilsAsync(byte slaveId, int startAddress, bool[] values)
    {
        LastUsed = DateTime.UtcNow;
        return _pooledConnection.Connection.WriteMultipleCoilsAsync(slaveId, startAddress, values);
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            // 返回到连接池而不是释放
            _pool.ReturnConnection(_pooledConnection.Connection);
            _disposed = true;
        }
    }
}